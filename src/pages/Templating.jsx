import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, ArrowLeft, Search, Filter } from 'lucide-react'
import TemplateList from '../components/Templating/TemplateList'
import TemplateEditor from '../components/Templating/TemplateEditor'
import Button from '../components/UI/Button'
import { useToast } from '../components/UI/NotificationSystem'
import { templatesApi } from '../api/mockApi'
import { COLORS, SPACING, TYPOGRAPHY } from '../utils/constants'

const Templating = () => {
  const [currentView, setCurrentView] = useState('list') // 'list' or 'editor'
  const [templates, setTemplates] = useState([])
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterActive, setFilterActive] = useState('all') // 'all', 'active', 'inactive'
  const [testResult, setTestResult] = useState(null)
  const [isLoading, setIsLoading] = useState(false)
  const toast = useToast()

  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      const templateData = await templatesApi.getTemplates()
      setTemplates(templateData)
    } catch (error) {
      console.error('Failed to load templates:', error)
    }
  }

  const handleCreateNew = () => {
    setSelectedTemplate({
      id: null,
      name: 'New Template',
      description: 'A new template for content automation',
      code: '',
      isActive: false,
      lastRun: null,
      createdAt: new Date().toISOString()
    })
    setCurrentView('editor')
    setTestResult(null)
  }

  const handleEditTemplate = (templateId) => {
    const template = templates.find(t => t.id === templateId)
    if (template) {
      setSelectedTemplate(template)
      setCurrentView('editor')
      setTestResult(null)
    }
  }

  const handleDeleteTemplate = async (templateId) => {
    if (window.confirm('Are you sure you want to delete this template?')) {
      try {
        await templatesApi.deleteTemplate(templateId)
        setTemplates(prev => prev.filter(t => t.id !== templateId))
        toast.success('Template deleted successfully!')
      } catch (error) {
        console.error('Failed to delete template:', error)
        toast.error('Failed to delete template')
      }
    }
  }

  const handleToggleActive = async (templateId) => {
    try {
      const template = templates.find(t => t.id === templateId)
      const updates = { isActive: !template.isActive }

      await templatesApi.updateTemplate(templateId, updates)
      setTemplates(prev =>
        prev.map(t =>
          t.id === templateId ? { ...t, ...updates } : t
        )
      )
    } catch (error) {
      console.error('Failed to toggle template status:', error)
    }
  }

  const handleTestTemplate = async (templateId, testData) => {
    setIsLoading(true)
    try {
      const result = await templatesApi.testTemplate(templateId, testData)
      setTestResult(result)
    } catch (error) {
      console.error('Failed to test template:', error)
      setTestResult({
        success: false,
        error: 'Failed to test template'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSaveTemplate = async (template) => {
    try {
      if (template.id) {
        // Update existing template
        await templatesApi.updateTemplate(template.id, template)
        setTemplates(prev =>
          prev.map(t =>
            t.id === template.id ? { ...t, ...template } : t
          )
        )
        toast.success('Template updated successfully!')
      } else {
        // Create new template
        const newTemplate = await templatesApi.createTemplate(template)
        setTemplates(prev => [...prev, newTemplate])
        setSelectedTemplate(newTemplate)
        toast.success('Template created successfully!')
      }
    } catch (error) {
      console.error('Failed to save template:', error)
      toast.error('Failed to save template')
    }
  }

  const handleDeployTemplate = async (templateId) => {
    try {
      await templatesApi.deployTemplate(templateId)
      setTemplates(prev =>
        prev.map(t =>
          t.id === templateId ? { ...t, isActive: true } : t
        )
      )
      toast.success('Template deployed successfully!')
    } catch (error) {
      console.error('Failed to deploy template:', error)
      toast.error('Failed to deploy template')
    }
  }

  const handleBackToList = () => {
    setCurrentView('list')
    setSelectedTemplate(null)
    setTestResult(null)
  }

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchQuery.toLowerCase())

    const matchesFilter = filterActive === 'all' ||
                         (filterActive === 'active' && template.isActive) ||
                         (filterActive === 'inactive' && !template.isActive)

    return matchesSearch && matchesFilter
  })

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="templating"
    >
      <AnimatePresence mode="wait">
        {currentView === 'list' ? (
          <motion.div
            key="list"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Header */}
            <div className="page-header">
              <div className="header-content">
                <div className="header-text">
                  <h1>Templates</h1>
                  <p>Create and manage advanced content templates with code.</p>
                </div>

                <Button
                  variant="primary"
                  icon={<Plus size={20} />}
                  onClick={handleCreateNew}
                >
                  Create Template
                </Button>
              </div>
            </div>

            {/* Filters */}
            <div className="filters-section">
              <div className="search-bar">
                <Search size={20} className="search-icon" />
                <input
                  type="text"
                  placeholder="Search templates..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="search-input"
                />
              </div>

              <div className="filter-buttons">
                <button
                  className={`filter-button ${filterActive === 'all' ? 'active' : ''}`}
                  onClick={() => setFilterActive('all')}
                >
                  All ({templates.length})
                </button>
                <button
                  className={`filter-button ${filterActive === 'active' ? 'active' : ''}`}
                  onClick={() => setFilterActive('active')}
                >
                  Active ({templates.filter(t => t.isActive).length})
                </button>
                <button
                  className={`filter-button ${filterActive === 'inactive' ? 'active' : ''}`}
                  onClick={() => setFilterActive('inactive')}
                >
                  Inactive ({templates.filter(t => !t.isActive).length})
                </button>
              </div>
            </div>

            {/* Template List */}
            <div className="templates-section">
              <TemplateList
                templates={filteredTemplates}
                onEdit={handleEditTemplate}
                onDelete={handleDeleteTemplate}
                onToggleActive={handleToggleActive}
                onTest={(templateId) => {
                  handleEditTemplate(templateId)
                  // Switch to test tab after a short delay
                  setTimeout(() => {
                    setCurrentView('editor')
                  }, 100)
                }}
                onCreateNew={handleCreateNew}
              />
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="editor"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {/* Editor Header */}
            <div className="editor-header">
              <Button
                variant="ghost"
                icon={<ArrowLeft size={20} />}
                onClick={handleBackToList}
              >
                Back to Templates
              </Button>
            </div>

            {/* Template Editor */}
            <TemplateEditor
              template={selectedTemplate}
              onSave={handleSaveTemplate}
              onTest={handleTestTemplate}
              onDeploy={handleDeployTemplate}
              testResult={testResult}
              isLoading={isLoading}
            />
          </motion.div>
        )}
      </AnimatePresence>

      <style jsx>{`
        .templating {
          padding: 0;
        }

        .page-header {
          margin-bottom: ${SPACING['2xl']};
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: ${SPACING.lg};
        }

        .header-text h1 {
          font-size: ${TYPOGRAPHY.fontSize['3xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.sm} 0;
        }

        .header-text p {
          color: ${COLORS.textSecondary};
          margin: 0;
          font-size: ${TYPOGRAPHY.fontSize.lg};
        }

        .filters-section {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: ${SPACING.lg};
          margin-bottom: ${SPACING['2xl']};
          padding: ${SPACING.lg};
          background: ${COLORS.background};
          border-radius: ${SPACING.lg};
          border: 1px solid ${COLORS.gray200};
        }

        .search-bar {
          position: relative;
          flex: 1;
          max-width: 400px;
        }

        .search-icon {
          position: absolute;
          left: ${SPACING.md};
          top: 50%;
          transform: translateY(-50%);
          color: ${COLORS.textMuted};
        }

        .search-input {
          width: 100%;
          padding: ${SPACING.md} ${SPACING.md} ${SPACING.md} 2.5rem;
          border: 1px solid ${COLORS.gray300};
          border-radius: ${SPACING.lg};
          font-size: ${TYPOGRAPHY.fontSize.base};
          transition: border-color 0.2s ease;
        }

        .search-input:focus {
          outline: none;
          border-color: ${COLORS.primary};
          box-shadow: 0 0 0 3px ${COLORS.primary}20;
        }

        .filter-buttons {
          display: flex;
          gap: ${SPACING.sm};
        }

        .filter-button {
          padding: ${SPACING.sm} ${SPACING.md};
          border: 1px solid ${COLORS.gray300};
          background: ${COLORS.background};
          color: ${COLORS.textSecondary};
          border-radius: ${SPACING.md};
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .filter-button:hover {
          border-color: ${COLORS.gray400};
          color: ${COLORS.textPrimary};
        }

        .filter-button.active {
          background: ${COLORS.primary};
          border-color: ${COLORS.primary};
          color: white;
        }

        .templates-section {
          min-height: 400px;
        }

        .editor-header {
          margin-bottom: ${SPACING.lg};
        }

        @media (max-width: 768px) {
          .header-content {
            flex-direction: column;
            align-items: stretch;
          }

          .filters-section {
            flex-direction: column;
            align-items: stretch;
            gap: ${SPACING.md};
          }

          .filter-buttons {
            justify-content: center;
            flex-wrap: wrap;
          }
        }
      `}</style>
    </motion.div>
  )
}

export default Templating
