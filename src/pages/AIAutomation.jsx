import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, RefreshCw } from 'lucide-react'
import PostGenerationForm from '../components/AI/PostGenerationForm'
import GenerationLoading from '../components/AI/GenerationLoading'
import GeneratedPostCard from '../components/AI/GeneratedPostCard'
import Button from '../components/UI/Button'
import { useToast } from '../components/UI/NotificationSystem'
import { aiApi, postsApi, schedulingApi } from '../api/mockApi'
import { COLORS, SPACING, TYPOGRAPHY } from '../utils/constants'

const AIAutomation = () => {
  const [currentStep, setCurrentStep] = useState('form') // 'form', 'generating', 'review'
  const [generatedPosts, setGeneratedPosts] = useState([])
  const [isGenerating, setIsGenerating] = useState(false)
  const [lastPrompt, setLastPrompt] = useState({ niche: '', prompt: '' })
  const toast = useToast()

  const handleGenerate = async (niche, prompt) => {
    setIsGenerating(true)
    setCurrentStep('generating')
    setLastPrompt({ niche, prompt })

    try {
      const posts = await aiApi.generatePosts(niche, prompt)
      setGeneratedPosts(posts)
      setCurrentStep('review')
    } catch (error) {
      console.error('Failed to generate posts:', error)
      setCurrentStep('form')
    } finally {
      setIsGenerating(false)
    }
  }

  const handleEditPost = async (postId, updates) => {
    try {
      await postsApi.editPost(postId, updates)
      setGeneratedPosts(prev =>
        prev.map(post =>
          post.id === postId ? { ...post, ...updates } : post
        )
      )
      toast.success('Post updated successfully!')
    } catch (error) {
      console.error('Failed to edit post:', error)
      toast.error('Failed to update post')
    }
  }

  const handleDeletePost = async (postId) => {
    try {
      await postsApi.deletePost(postId)
      setGeneratedPosts(prev => prev.filter(post => post.id !== postId))
      toast.success('Post deleted successfully!')
    } catch (error) {
      console.error('Failed to delete post:', error)
      toast.error('Failed to delete post')
    }
  }

  const handleApprovePost = async (postId) => {
    try {
      await postsApi.approvePost(postId)
      setGeneratedPosts(prev =>
        prev.map(post =>
          post.id === postId ? { ...post, status: 'approved' } : post
        )
      )
      toast.success('Post approved!')
    } catch (error) {
      console.error('Failed to approve post:', error)
      toast.error('Failed to approve post')
    }
  }

  const handleSchedulePost = async (postId) => {
    try {
      const scheduledTime = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      await schedulingApi.schedulePost(postId, scheduledTime)
      // Remove from generated posts as it's now scheduled
      setGeneratedPosts(prev => prev.filter(post => post.id !== postId))
      toast.success('Post scheduled for tomorrow!')
    } catch (error) {
      console.error('Failed to schedule post:', error)
      toast.error('Failed to schedule post')
    }
  }

  const handlePublishNow = async (postId) => {
    try {
      await postsApi.publishNow(postId)
      // Remove from generated posts as it's now published
      setGeneratedPosts(prev => prev.filter(post => post.id !== postId))
      toast.success('Post published successfully!')
    } catch (error) {
      console.error('Failed to publish post:', error)
      toast.error('Failed to publish post')
    }
  }

  const handleBackToForm = () => {
    setCurrentStep('form')
    setGeneratedPosts([])
  }

  const handleRegenerateAll = () => {
    if (lastPrompt.niche && lastPrompt.prompt) {
      handleGenerate(lastPrompt.niche, lastPrompt.prompt)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="ai-automation"
    >
      {/* Header */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-text">
            <h1>AI Automation</h1>
            <p>Generate engaging content with AI-powered automation.</p>
          </div>

          {currentStep === 'review' && (
            <div className="header-actions">
              <Button
                variant="ghost"
                icon={<ArrowLeft size={20} />}
                onClick={handleBackToForm}
              >
                Back to Form
              </Button>
              <Button
                variant="outline"
                icon={<RefreshCw size={20} />}
                onClick={handleRegenerateAll}
                disabled={isGenerating}
              >
                Regenerate All
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="page-content">
        <AnimatePresence mode="wait">
          {currentStep === 'form' && (
            <motion.div
              key="form"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <PostGenerationForm
                onGenerate={handleGenerate}
                isGenerating={isGenerating}
              />
            </motion.div>
          )}

          {currentStep === 'generating' && (
            <motion.div
              key="generating"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 1.05 }}
              transition={{ duration: 0.3 }}
            >
              <GenerationLoading />
            </motion.div>
          )}

          {currentStep === 'review' && (
            <motion.div
              key="review"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="review-section"
            >
              <div className="review-header">
                <h2>Review Generated Posts</h2>
                <p>
                  {generatedPosts.length} posts generated.
                  Review, edit, and approve the ones you like.
                </p>
              </div>

              <div className="posts-grid">
                {generatedPosts.map((post, index) => (
                  <motion.div
                    key={post.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                  >
                    <GeneratedPostCard
                      post={post}
                      onEdit={handleEditPost}
                      onDelete={handleDeletePost}
                      onApprove={handleApprovePost}
                      onSchedule={handleSchedulePost}
                      onPublishNow={handlePublishNow}
                    />
                  </motion.div>
                ))}
              </div>

              {generatedPosts.length === 0 && (
                <div className="empty-state">
                  <p>All posts have been processed!</p>
                  <Button
                    variant="primary"
                    onClick={handleBackToForm}
                  >
                    Generate More Posts
                  </Button>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <style jsx>{`
        .ai-automation {
          padding: 0;
        }

        .page-header {
          margin-bottom: ${SPACING['2xl']};
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: ${SPACING.lg};
        }

        .header-text h1 {
          font-size: ${TYPOGRAPHY.fontSize['3xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.sm} 0;
        }

        .header-text p {
          color: ${COLORS.textSecondary};
          margin: 0;
          font-size: ${TYPOGRAPHY.fontSize.lg};
        }

        .header-actions {
          display: flex;
          gap: ${SPACING.md};
        }

        .page-content {
          min-height: 400px;
        }

        .review-section {
          max-width: 1200px;
          margin: 0 auto;
        }

        .review-header {
          text-align: center;
          margin-bottom: ${SPACING['2xl']};
        }

        .review-header h2 {
          font-size: ${TYPOGRAPHY.fontSize['2xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.sm} 0;
        }

        .review-header p {
          color: ${COLORS.textSecondary};
          margin: 0;
        }

        .posts-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
          gap: ${SPACING.xl};
        }

        .empty-state {
          text-align: center;
          padding: ${SPACING['3xl']};
          color: ${COLORS.textSecondary};
        }

        .empty-state p {
          margin: 0 0 ${SPACING.lg} 0;
          font-size: ${TYPOGRAPHY.fontSize.lg};
        }

        @media (max-width: 768px) {
          .header-content {
            flex-direction: column;
            align-items: stretch;
          }

          .header-actions {
            justify-content: center;
          }

          .posts-grid {
            grid-template-columns: 1fr;
            gap: ${SPACING.lg};
          }
        }
      `}</style>
    </motion.div>
  )
}

export default AIAutomation
