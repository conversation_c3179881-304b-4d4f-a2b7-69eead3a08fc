import { useState } from 'react'
import { motion } from 'framer-motion'
import {
  <PERSON>r,
  Bell,
  Shield,
  Palette,
  Twitter,
  Save,
  LogOut,
  Trash2
} from 'lucide-react'
import Card from '../components/UI/Card'
import Button from '../components/UI/Button'
import { useToast } from '../components/UI/NotificationSystem'
import { mockUser } from '../data/mockData'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../utils/constants'

const Settings = () => {
  const [activeTab, setActiveTab] = useState('profile')
  const [settings, setSettings] = useState({
    profile: {
      name: mockUser.name,
      email: mockUser.email,
      twitterHandle: mockUser.twitterHandle,
      bio: 'Content creator and AI enthusiast'
    },
    notifications: {
      emailNotifications: true,
      pushNotifications: true,
      weeklyReports: true,
      postReminders: false
    },
    privacy: {
      profilePublic: false,
      analyticsSharing: true,
      dataExport: false
    },
    appearance: {
      theme: 'light',
      compactMode: false,
      animations: true
    }
  })

  const toast = useToast()

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'privacy', name: 'Privacy', icon: Shield },
    { id: 'appearance', name: 'Appearance', icon: Palette }
  ]

  const handleSave = () => {
    // Simulate saving settings
    setTimeout(() => {
      toast.success('Settings saved successfully!')
    }, 500)
  }

  const handleSettingChange = (category, key, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }))
  }

  const renderProfileSettings = () => (
    <div className="settings-section">
      <h3>Profile Information</h3>

      <div className="form-group">
        <label>Full Name</label>
        <input
          type="text"
          value={settings.profile.name}
          onChange={(e) => handleSettingChange('profile', 'name', e.target.value)}
          className="form-input"
        />
      </div>

      <div className="form-group">
        <label>Email Address</label>
        <input
          type="email"
          value={settings.profile.email}
          onChange={(e) => handleSettingChange('profile', 'email', e.target.value)}
          className="form-input"
        />
      </div>

      <div className="form-group">
        <label>Twitter Handle</label>
        <div className="input-with-icon">
          <Twitter size={16} className="input-icon" />
          <input
            type="text"
            value={settings.profile.twitterHandle}
            onChange={(e) => handleSettingChange('profile', 'twitterHandle', e.target.value)}
            className="form-input with-icon"
            placeholder="@username"
          />
        </div>
      </div>

      <div className="form-group">
        <label>Bio</label>
        <textarea
          value={settings.profile.bio}
          onChange={(e) => handleSettingChange('profile', 'bio', e.target.value)}
          className="form-textarea"
          rows={3}
          placeholder="Tell us about yourself..."
        />
      </div>
    </div>
  )

  const renderNotificationSettings = () => (
    <div className="settings-section">
      <h3>Notification Preferences</h3>

      {Object.entries(settings.notifications).map(([key, value]) => (
        <div key={key} className="toggle-group">
          <div className="toggle-info">
            <label className="toggle-label">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <p className="toggle-description">
              {getNotificationDescription(key)}
            </p>
          </div>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleSettingChange('notifications', key, e.target.checked)}
            />
            <span className="toggle-slider" />
          </label>
        </div>
      ))}
    </div>
  )

  const renderPrivacySettings = () => (
    <div className="settings-section">
      <h3>Privacy & Security</h3>

      {Object.entries(settings.privacy).map(([key, value]) => (
        <div key={key} className="toggle-group">
          <div className="toggle-info">
            <label className="toggle-label">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <p className="toggle-description">
              {getPrivacyDescription(key)}
            </p>
          </div>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleSettingChange('privacy', key, e.target.checked)}
            />
            <span className="toggle-slider" />
          </label>
        </div>
      ))}

      <div className="danger-zone">
        <h4>Danger Zone</h4>
        <div className="danger-actions">
          <Button
            variant="outline"
            icon={<LogOut size={16} />}
            onClick={() => toast.info('Sign out functionality would be implemented here')}
          >
            Sign Out
          </Button>
          <Button
            variant="danger"
            icon={<Trash2 size={16} />}
            onClick={() => toast.warning('Account deletion would require confirmation')}
          >
            Delete Account
          </Button>
        </div>
      </div>
    </div>
  )

  const renderAppearanceSettings = () => (
    <div className="settings-section">
      <h3>Appearance & Behavior</h3>

      <div className="form-group">
        <label>Theme</label>
        <select
          value={settings.appearance.theme}
          onChange={(e) => handleSettingChange('appearance', 'theme', e.target.value)}
          className="form-select"
        >
          <option value="light">Light</option>
          <option value="dark">Dark</option>
          <option value="system">System</option>
        </select>
      </div>

      {Object.entries(settings.appearance).filter(([key]) => key !== 'theme').map(([key, value]) => (
        <div key={key} className="toggle-group">
          <div className="toggle-info">
            <label className="toggle-label">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <p className="toggle-description">
              {getAppearanceDescription(key)}
            </p>
          </div>
          <label className="toggle-switch">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => handleSettingChange('appearance', key, e.target.checked)}
            />
            <span className="toggle-slider" />
          </label>
        </div>
      ))}
    </div>
  )

  const getNotificationDescription = (key) => {
    const descriptions = {
      emailNotifications: 'Receive email updates about your account activity',
      pushNotifications: 'Get browser notifications for important events',
      weeklyReports: 'Weekly summary of your content performance',
      postReminders: 'Reminders when scheduled posts are about to go live'
    }
    return descriptions[key] || ''
  }

  const getPrivacyDescription = (key) => {
    const descriptions = {
      profilePublic: 'Make your profile visible to other users',
      analyticsSharing: 'Share anonymous usage data to improve the service',
      dataExport: 'Allow exporting your data in standard formats'
    }
    return descriptions[key] || ''
  }

  const getAppearanceDescription = (key) => {
    const descriptions = {
      compactMode: 'Use a more compact layout to fit more content',
      animations: 'Enable smooth animations and transitions'
    }
    return descriptions[key] || ''
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="settings"
    >
      {/* Header */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-text">
            <h1>Settings</h1>
            <p>Manage your account, preferences, and integrations.</p>
          </div>

          <Button
            variant="primary"
            icon={<Save size={20} />}
            onClick={handleSave}
          >
            Save Changes
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="settings-content">
        {/* Sidebar */}
        <div className="settings-sidebar">
          <nav className="settings-nav">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  className={`nav-item ${activeTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTab(tab.id)}
                >
                  <Icon size={20} />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Main Content */}
        <div className="settings-main">
          <Card className="settings-card">
            {activeTab === 'profile' && renderProfileSettings()}
            {activeTab === 'notifications' && renderNotificationSettings()}
            {activeTab === 'privacy' && renderPrivacySettings()}
            {activeTab === 'appearance' && renderAppearanceSettings()}
          </Card>
        </div>
      </div>

      <style jsx>{`
        .settings {
          padding: 0;
        }

        .page-header {
          margin-bottom: ${SPACING['2xl']};
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: ${SPACING.lg};
        }

        .header-text h1 {
          font-size: ${TYPOGRAPHY.fontSize['3xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.sm} 0;
        }

        .header-text p {
          color: ${COLORS.textSecondary};
          margin: 0;
          font-size: ${TYPOGRAPHY.fontSize.lg};
        }

        .settings-content {
          display: grid;
          grid-template-columns: 280px 1fr;
          gap: ${SPACING['2xl']};
        }

        .settings-sidebar {
          position: sticky;
          top: ${SPACING.lg};
          height: fit-content;
        }

        .settings-nav {
          background: ${COLORS.background};
          border: 1px solid ${COLORS.gray200};
          border-radius: ${BORDER_RADIUS.xl};
          overflow: hidden;
        }

        .nav-item {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
          width: 100%;
          padding: ${SPACING.lg};
          border: none;
          background: none;
          text-align: left;
          cursor: pointer;
          transition: all 0.2s ease;
          color: ${COLORS.textSecondary};
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .nav-item:last-child {
          border-bottom: none;
        }

        .nav-item:hover {
          background: ${COLORS.gray50};
          color: ${COLORS.textPrimary};
        }

        .nav-item.active {
          background: ${COLORS.primary}10;
          color: ${COLORS.primary};
          border-right: 3px solid ${COLORS.primary};
        }

        .settings-main {
          min-height: 600px;
        }

        .settings-card {
          padding: ${SPACING['2xl']};
        }

        .settings-section h3 {
          font-size: ${TYPOGRAPHY.fontSize.xl};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.xl} 0;
          padding-bottom: ${SPACING.md};
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .form-group {
          margin-bottom: ${SPACING.lg};
        }

        .form-group label {
          display: block;
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          color: ${COLORS.textPrimary};
          margin-bottom: ${SPACING.sm};
        }

        .form-input,
        .form-textarea,
        .form-select {
          width: 100%;
          padding: ${SPACING.md};
          border: 1px solid ${COLORS.gray300};
          border-radius: ${BORDER_RADIUS.lg};
          font-size: ${TYPOGRAPHY.fontSize.base};
          transition: border-color 0.2s ease;
        }

        .form-input:focus,
        .form-textarea:focus,
        .form-select:focus {
          outline: none;
          border-color: ${COLORS.primary};
          box-shadow: 0 0 0 3px ${COLORS.primary}20;
        }

        .input-with-icon {
          position: relative;
        }

        .input-icon {
          position: absolute;
          left: ${SPACING.md};
          top: 50%;
          transform: translateY(-50%);
          color: ${COLORS.textMuted};
        }

        .form-input.with-icon {
          padding-left: 2.5rem;
        }

        .toggle-group {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: ${SPACING.lg} 0;
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .toggle-group:last-child {
          border-bottom: none;
        }

        .toggle-info {
          flex: 1;
          margin-right: ${SPACING.lg};
        }

        .toggle-label {
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          color: ${COLORS.textPrimary};
          margin-bottom: ${SPACING.xs};
        }

        .toggle-description {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          color: ${COLORS.textSecondary};
          margin: 0;
        }

        .toggle-switch {
          position: relative;
          display: inline-block;
          width: 44px;
          height: 24px;
          cursor: pointer;
        }

        .toggle-switch input {
          opacity: 0;
          width: 0;
          height: 0;
        }

        .toggle-slider {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: ${COLORS.gray300};
          transition: 0.3s;
          border-radius: 24px;
        }

        .toggle-slider:before {
          position: absolute;
          content: "";
          height: 18px;
          width: 18px;
          left: 3px;
          bottom: 3px;
          background-color: white;
          transition: 0.3s;
          border-radius: 50%;
        }

        .toggle-switch input:checked + .toggle-slider {
          background-color: ${COLORS.primary};
        }

        .toggle-switch input:checked + .toggle-slider:before {
          transform: translateX(20px);
        }

        .danger-zone {
          margin-top: ${SPACING['2xl']};
          padding-top: ${SPACING.xl};
          border-top: 1px solid ${COLORS.error}30;
        }

        .danger-zone h4 {
          color: ${COLORS.error};
          margin: 0 0 ${SPACING.lg} 0;
          font-size: ${TYPOGRAPHY.fontSize.lg};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
        }

        .danger-actions {
          display: flex;
          gap: ${SPACING.md};
        }

        @media (max-width: 1024px) {
          .settings-content {
            grid-template-columns: 1fr;
            gap: ${SPACING.xl};
          }

          .settings-sidebar {
            position: static;
          }

          .settings-nav {
            display: flex;
            overflow-x: auto;
          }

          .nav-item {
            white-space: nowrap;
            border-bottom: none;
            border-right: 1px solid ${COLORS.gray200};
          }

          .nav-item:last-child {
            border-right: none;
          }
        }

        @media (max-width: 768px) {
          .header-content {
            flex-direction: column;
            align-items: stretch;
          }

          .danger-actions {
            flex-direction: column;
          }
        }
      `}</style>
    </motion.div>
  )
}

export default Settings
