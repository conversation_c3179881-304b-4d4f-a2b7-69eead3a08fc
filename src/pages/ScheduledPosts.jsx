import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Calendar as CalendarIcon, List, Plus, Filter } from 'lucide-react'
import Calendar from '../components/Scheduling/Calendar'
import ScheduledPostsList from '../components/Scheduling/ScheduledPostsList'
import TimeSlotPicker from '../components/Scheduling/TimeSlotPicker'
import Button from '../components/UI/Button'
import { useToast } from '../components/UI/NotificationSystem'
import { postsApi, schedulingApi } from '../api/mockApi'
import { COLORS, SPACING, TYPOGRAPHY } from '../utils/constants'
import { isToday, isFuture, isPast } from 'date-fns'

const ScheduledPosts = () => {
  const [currentView, setCurrentView] = useState('calendar') // 'calendar' or 'list'
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [scheduledPosts, setScheduledPosts] = useState([])
  const [showTimeSlotPicker, setShowTimeSlotPicker] = useState(false)
  const [selectedPost, setSelectedPost] = useState(null)
  const [filterStatus, setFilterStatus] = useState('all') // 'all', 'upcoming', 'today', 'past'
  const [loading, setLoading] = useState(true)
  const toast = useToast()

  useEffect(() => {
    loadScheduledPosts()
  }, [])

  const loadScheduledPosts = async () => {
    setLoading(true)
    try {
      const posts = await postsApi.getScheduledPosts()
      setScheduledPosts(posts)
    } catch (error) {
      console.error('Failed to load scheduled posts:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDateSelect = (date) => {
    setSelectedDate(date)
  }

  const handleCreatePost = (date) => {
    setSelectedDate(date)
    setSelectedPost(null)
    setShowTimeSlotPicker(true)
  }

  const handleTimeSelect = async (scheduledDateTime) => {
    try {
      // In a real app, this would create a new post or schedule an existing one
      const newPost = {
        id: `scheduled_${Date.now()}`,
        text: 'New scheduled post - edit this content',
        media: null,
        scheduledFor: scheduledDateTime.toISOString(),
        status: 'scheduled',
        niche: 'General',
        createdAt: new Date().toISOString()
      }

      setScheduledPosts(prev => [...prev, newPost])
      setShowTimeSlotPicker(false)
      toast.success('Post scheduled successfully!')
    } catch (error) {
      console.error('Failed to schedule post:', error)
    }
  }

  const handleEditPost = (postId) => {
    console.log('Edit post:', postId)
    // This would open an edit modal
  }

  const handleDeletePost = async (postId) => {
    if (window.confirm('Are you sure you want to delete this scheduled post?')) {
      try {
        await postsApi.deletePost(postId)
        setScheduledPosts(prev => prev.filter(post => post.id !== postId))
      } catch (error) {
        console.error('Failed to delete post:', error)
      }
    }
  }

  const handleReschedulePost = (postId) => {
    const post = scheduledPosts.find(p => p.id === postId)
    if (post) {
      setSelectedPost(post)
      setSelectedDate(new Date(post.scheduledFor))
      setShowTimeSlotPicker(true)
    }
  }

  const handlePublishNow = async (postId) => {
    try {
      await postsApi.publishNow(postId)
      setScheduledPosts(prev => prev.filter(post => post.id !== postId))
      console.log('Post published successfully!')
    } catch (error) {
      console.error('Failed to publish post:', error)
    }
  }

  const handlePausePost = async (postId) => {
    try {
      const post = scheduledPosts.find(p => p.id === postId)
      const newStatus = post.status === 'paused' ? 'scheduled' : 'paused'

      await postsApi.editPost(postId, { status: newStatus })
      setScheduledPosts(prev =>
        prev.map(p =>
          p.id === postId ? { ...p, status: newStatus } : p
        )
      )
    } catch (error) {
      console.error('Failed to update post status:', error)
    }
  }

  const getFilteredPosts = () => {
    const now = new Date()

    switch (filterStatus) {
      case 'today':
        return scheduledPosts.filter(post =>
          isToday(new Date(post.scheduledFor))
        )
      case 'upcoming':
        return scheduledPosts.filter(post =>
          isFuture(new Date(post.scheduledFor))
        )
      case 'past':
        return scheduledPosts.filter(post =>
          isPast(new Date(post.scheduledFor)) && !isToday(new Date(post.scheduledFor))
        )
      default:
        return scheduledPosts
    }
  }

  const getPostsForSelectedDate = () => {
    return scheduledPosts.filter(post =>
      new Date(post.scheduledFor).toDateString() === selectedDate.toDateString()
    )
  }

  const filteredPosts = getFilteredPosts()
  const postsForSelectedDate = getPostsForSelectedDate()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="scheduled-posts"
    >
      {/* Header */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-text">
            <h1>Scheduled Posts</h1>
            <p>Manage your scheduled content and posting calendar.</p>
          </div>

          <div className="header-actions">
            <div className="view-toggle">
              <button
                className={`toggle-btn ${currentView === 'calendar' ? 'active' : ''}`}
                onClick={() => setCurrentView('calendar')}
              >
                <CalendarIcon size={16} />
                Calendar
              </button>
              <button
                className={`toggle-btn ${currentView === 'list' ? 'active' : ''}`}
                onClick={() => setCurrentView('list')}
              >
                <List size={16} />
                List
              </button>
            </div>

            <Button
              variant="primary"
              icon={<Plus size={20} />}
              onClick={() => handleCreatePost(new Date())}
            >
              Schedule Post
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="page-content">
        {currentView === 'calendar' ? (
          <div className="calendar-view">
            <div className="calendar-section">
              <Calendar
                selectedDate={selectedDate}
                onDateSelect={handleDateSelect}
                scheduledPosts={scheduledPosts}
                onCreatePost={handleCreatePost}
              />
            </div>

            <div className="sidebar-section">
              <div className="sidebar-header">
                <h3>
                  Posts for {selectedDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric'
                  })}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  icon={<Plus size={14} />}
                  onClick={() => handleCreatePost(selectedDate)}
                >
                  Add Post
                </Button>
              </div>

              <ScheduledPostsList
                posts={postsForSelectedDate}
                onEdit={handleEditPost}
                onDelete={handleDeletePost}
                onReschedule={handleReschedulePost}
                onPublishNow={handlePublishNow}
                onPause={handlePausePost}
                loading={loading}
              />
            </div>
          </div>
        ) : (
          <div className="list-view">
            {/* Filters */}
            <div className="list-filters">
              <div className="filter-buttons">
                <button
                  className={`filter-btn ${filterStatus === 'all' ? 'active' : ''}`}
                  onClick={() => setFilterStatus('all')}
                >
                  All ({scheduledPosts.length})
                </button>
                <button
                  className={`filter-btn ${filterStatus === 'today' ? 'active' : ''}`}
                  onClick={() => setFilterStatus('today')}
                >
                  Today ({scheduledPosts.filter(p => isToday(new Date(p.scheduledFor))).length})
                </button>
                <button
                  className={`filter-btn ${filterStatus === 'upcoming' ? 'active' : ''}`}
                  onClick={() => setFilterStatus('upcoming')}
                >
                  Upcoming ({scheduledPosts.filter(p => isFuture(new Date(p.scheduledFor))).length})
                </button>
                <button
                  className={`filter-btn ${filterStatus === 'past' ? 'active' : ''}`}
                  onClick={() => setFilterStatus('past')}
                >
                  Past ({scheduledPosts.filter(p => isPast(new Date(p.scheduledFor)) && !isToday(new Date(p.scheduledFor))).length})
                </button>
              </div>
            </div>

            {/* Posts List */}
            <ScheduledPostsList
              posts={filteredPosts}
              onEdit={handleEditPost}
              onDelete={handleDeletePost}
              onReschedule={handleReschedulePost}
              onPublishNow={handlePublishNow}
              onPause={handlePausePost}
              loading={loading}
            />
          </div>
        )}
      </div>

      {/* Time Slot Picker Modal */}
      {showTimeSlotPicker && (
        <TimeSlotPicker
          selectedDate={selectedDate}
          selectedPost={selectedPost}
          onTimeSelect={handleTimeSelect}
          onClose={() => setShowTimeSlotPicker(false)}
        />
      )}

      <style jsx>{`
        .scheduled-posts {
          padding: 0;
        }

        .page-header {
          margin-bottom: ${SPACING['2xl']};
        }

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: ${SPACING.lg};
        }

        .header-text h1 {
          font-size: ${TYPOGRAPHY.fontSize['3xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.sm} 0;
        }

        .header-text p {
          color: ${COLORS.textSecondary};
          margin: 0;
          font-size: ${TYPOGRAPHY.fontSize.lg};
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: ${SPACING.lg};
        }

        .view-toggle {
          display: flex;
          border: 1px solid ${COLORS.gray300};
          border-radius: ${SPACING.md};
          overflow: hidden;
        }

        .toggle-btn {
          display: flex;
          align-items: center;
          gap: ${SPACING.sm};
          padding: ${SPACING.sm} ${SPACING.md};
          border: none;
          background: ${COLORS.background};
          color: ${COLORS.textSecondary};
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .toggle-btn:hover {
          background: ${COLORS.gray100};
          color: ${COLORS.textPrimary};
        }

        .toggle-btn.active {
          background: ${COLORS.primary};
          color: white;
        }

        .calendar-view {
          display: grid;
          grid-template-columns: 1fr 400px;
          gap: ${SPACING['2xl']};
          height: calc(100vh - 300px);
        }

        .calendar-section {
          min-height: 0;
        }

        .sidebar-section {
          display: flex;
          flex-direction: column;
          min-height: 0;
        }

        .sidebar-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: ${SPACING.lg};
          padding-bottom: ${SPACING.md};
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .sidebar-header h3 {
          font-size: ${TYPOGRAPHY.fontSize.lg};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          color: ${COLORS.textPrimary};
          margin: 0;
        }

        .list-view {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.xl};
        }

        .list-filters {
          display: flex;
          justify-content: center;
          padding: ${SPACING.lg};
          background: ${COLORS.background};
          border-radius: ${SPACING.lg};
          border: 1px solid ${COLORS.gray200};
        }

        .filter-buttons {
          display: flex;
          gap: ${SPACING.sm};
          flex-wrap: wrap;
          justify-content: center;
        }

        .filter-btn {
          padding: ${SPACING.sm} ${SPACING.md};
          border: 1px solid ${COLORS.gray300};
          background: ${COLORS.background};
          color: ${COLORS.textSecondary};
          border-radius: ${SPACING.md};
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .filter-btn:hover {
          border-color: ${COLORS.gray400};
          color: ${COLORS.textPrimary};
        }

        .filter-btn.active {
          background: ${COLORS.primary};
          border-color: ${COLORS.primary};
          color: white;
        }

        @media (max-width: 1024px) {
          .calendar-view {
            grid-template-columns: 1fr;
            gap: ${SPACING.xl};
          }

          .sidebar-section {
            max-height: 400px;
            overflow-y: auto;
          }
        }

        @media (max-width: 768px) {
          .header-content {
            flex-direction: column;
            align-items: stretch;
          }

          .header-actions {
            justify-content: space-between;
          }

          .filter-buttons {
            justify-content: flex-start;
          }
        }
      `}</style>
    </motion.div>
  )
}

export default ScheduledPosts
