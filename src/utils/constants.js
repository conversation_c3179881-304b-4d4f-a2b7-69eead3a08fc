// Constants for TweetCrafter application

// App configuration
export const APP_CONFIG = {
  name: 'TweetCrafter',
  version: '1.0.0',
  description: 'AI-powered Twitter content automation platform'
};

// Color palette following the README specifications
export const COLORS = {
  primary: '#3B82F6', // Bright blue
  secondary: '#10B981', // Teal
  accent: '#8B5CF6', // Purple
  
  // Grays
  gray50: '#F9FAFB',
  gray100: '#F3F4F6',
  gray200: '#E5E7EB',
  gray300: '#D1D5DB',
  gray400: '#9CA3AF',
  gray500: '#6B7280',
  gray600: '#4B5563',
  gray700: '#374151',
  gray800: '#1F2937',
  gray900: '#111827',
  
  // Status colors
  success: '#10B981',
  warning: '#F59E0B',
  error: '#EF4444',
  info: '#3B82F6',
  
  // Background
  background: '#FFFFFF',
  backgroundSecondary: '#F9FAFB',
  
  // Text
  textPrimary: '#111827',
  textSecondary: '#6B7280',
  textMuted: '#9CA3AF'
};

// Typography following Apple/Inter font system
export const TYPOGRAPHY = {
  fontFamily: {
    primary: '-apple-system, BlinkMacSystemFont, "Inter", "Segoe UI", Roboto, sans-serif',
    mono: 'Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace'
  },
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
  },
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75'
  }
};

// Spacing system
export const SPACING = {
  xs: '0.25rem',   // 4px
  sm: '0.5rem',    // 8px
  md: '1rem',      // 16px
  lg: '1.5rem',    // 24px
  xl: '2rem',      // 32px
  '2xl': '3rem',   // 48px
  '3xl': '4rem',   // 64px
  '4xl': '6rem',   // 96px
};

// Border radius
export const BORDER_RADIUS = {
  sm: '0.25rem',   // 4px
  md: '0.375rem',  // 6px
  lg: '0.5rem',    // 8px
  xl: '0.75rem',   // 12px
  '2xl': '1rem',   // 16px
  full: '9999px'
};

// Shadows
export const SHADOWS = {
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  card: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
};

// Animation durations
export const ANIMATION = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
  slower: '750ms'
};

// Breakpoints for responsive design
export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
};

// Navigation items
export const NAVIGATION_ITEMS = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    path: '/',
    icon: 'Home'
  },
  {
    id: 'ai-automation',
    name: 'AI Automation',
    path: '/ai-automation',
    icon: 'Sparkles'
  },
  {
    id: 'templating',
    name: 'Templating',
    path: '/templating',
    icon: 'Code'
  },
  {
    id: 'scheduled',
    name: 'Scheduled Posts',
    path: '/scheduled',
    icon: 'Calendar'
  },
  {
    id: 'settings',
    name: 'Settings',
    path: '/settings',
    icon: 'Settings'
  }
];

// Post statuses
export const POST_STATUS = {
  PENDING: 'pending',
  APPROVED: 'approved',
  SCHEDULED: 'scheduled',
  PUBLISHED: 'published',
  DRAFT: 'draft'
};

// Performance levels
export const PERFORMANCE_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium',
  LOW: 'low'
};

// Template status
export const TEMPLATE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  TESTING: 'testing'
};

// Notification types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info'
};

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'tweetcrafter_auth_token',
  USER_DATA: 'tweetcrafter_user_data',
  THEME: 'tweetcrafter_theme',
  ONBOARDING_COMPLETED: 'tweetcrafter_onboarding_completed'
};

// API endpoints (for future real API integration)
export const API_ENDPOINTS = {
  AUTH: '/api/auth',
  USERS: '/api/users',
  POSTS: '/api/posts',
  TEMPLATES: '/api/templates',
  ANALYTICS: '/api/analytics',
  SCHEDULING: '/api/scheduling'
};

// Social media limits
export const SOCIAL_LIMITS = {
  TWITTER: {
    MAX_CHARACTERS: 280,
    MAX_IMAGES: 4,
    MAX_VIDEO_SIZE: '512MB'
  }
};

// Default values
export const DEFAULTS = {
  POSTS_PER_PAGE: 10,
  ANALYTICS_DAYS: 30,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
  NOTIFICATION_DURATION: 5000 // 5 seconds
};
