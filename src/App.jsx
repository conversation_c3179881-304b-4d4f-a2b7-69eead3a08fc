import { useState, useEffect } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { motion, AnimatePresence } from 'framer-motion'
import './App.css'

// Layout components
import Layout from './components/Layout/Layout'
import OnboardingFlow from './components/Onboarding/OnboardingFlow'
import { NotificationProvider } from './components/UI/NotificationSystem'

// Page components
import Dashboard from './pages/Dashboard'
import AIAutomation from './pages/AIAutomation'
import Templating from './pages/Templating'
import ScheduledPosts from './pages/ScheduledPosts'
import Settings from './pages/Settings'

// Utils
import { getLocalStorage, setLocalStorage } from './utils/helpers'
import { STORAGE_KEYS } from './utils/constants'

function App() {
  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if onboarding is complete
    const onboardingComplete = getLocalStorage(STORAGE_KEYS.ONBOARDING_COMPLETED, false)
    setIsOnboardingComplete(onboardingComplete)
    setIsLoading(false)
  }, [])

  const handleOnboardingComplete = () => {
    setIsOnboardingComplete(true)
    setLocalStorage(STORAGE_KEYS.ONBOARDING_COMPLETED, true)
  }

  if (isLoading) {
    return (
      <div className="loading-screen">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="loading-content"
        >
          <h1>TweetCrafter</h1>
          <div className="loading-spinner"></div>
        </motion.div>
      </div>
    )
  }

  if (!isOnboardingComplete) {
    return <OnboardingFlow onComplete={handleOnboardingComplete} />
  }

  return (
    <NotificationProvider>
      <div className="app">
        <Layout>
          <AnimatePresence mode="wait">
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/ai-automation" element={<AIAutomation />} />
              <Route path="/templating" element={<Templating />} />
              <Route path="/scheduled" element={<ScheduledPosts />} />
              <Route path="/settings" element={<Settings />} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </AnimatePresence>
        </Layout>
      </div>
    </NotificationProvider>
  )
}

export default App
