// Mock data for TweetCrafter application

// User data
export const mockUser = {
  id: 'user_1',
  name: '<PERSON>',
  email: '<EMAIL>',
  twitterHandle: '@alex<PERSON>hnson',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  isConnected: true,
  joinedDate: '2024-01-15',
  subscription: 'pro'
};

// Niches for AI automation
export const niches = [
  'Tech News',
  'Marketing',
  'Cryptocurrency',
  'AI & Machine Learning',
  'Web Development',
  'Startup Life',
  'Digital Marketing',
  'Social Media',
  'Productivity',
  'Design',
  'Business Strategy',
  'Personal Branding'
];

// Mock generated posts
export const mockGeneratedPosts = [
  {
    id: 'post_1',
    text: '🚀 Just discovered an amazing new AI tool that can automate your social media content creation! The future of marketing is here. What are your thoughts on AI-powered content? #AI #Marketing #SocialMedia',
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop',
      alt: 'AI technology concept'
    },
    status: 'pending',
    createdAt: '2024-08-10T10:30:00Z',
    niche: 'AI & Machine Learning',
    engagement: {
      likes: 0,
      retweets: 0,
      replies: 0
    }
  },
  {
    id: 'post_2',
    text: '💡 5 productivity hacks that changed my life:\n\n1. Time blocking\n2. The 2-minute rule\n3. Batch processing\n4. Digital minimalism\n5. Regular breaks\n\nWhich one resonates with you most? 🤔',
    media: null,
    status: 'pending',
    createdAt: '2024-08-10T11:15:00Z',
    niche: 'Productivity',
    engagement: {
      likes: 0,
      retweets: 0,
      replies: 0
    }
  },
  {
    id: 'post_3',
    text: '🎨 Design tip: White space is not wasted space. It\'s a powerful design element that:\n\n✅ Improves readability\n✅ Creates visual hierarchy\n✅ Reduces cognitive load\n✅ Makes content more scannable\n\n#DesignTips #UX',
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=300&fit=crop',
      alt: 'Clean design concept'
    },
    status: 'approved',
    createdAt: '2024-08-10T12:00:00Z',
    niche: 'Design',
    engagement: {
      likes: 0,
      retweets: 0,
      replies: 0
    }
  }
];

// Scheduled posts
export const mockScheduledPosts = [
  {
    id: 'scheduled_1',
    text: '🌟 Monday motivation: Your only limit is your mind. What\'s one goal you\'re working towards this week? Share it below! 👇 #MondayMotivation #Goals',
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop',
      alt: 'Motivational landscape'
    },
    scheduledFor: '2024-08-12T09:00:00Z',
    status: 'scheduled',
    niche: 'Personal Branding',
    createdAt: '2024-08-10T14:30:00Z'
  },
  {
    id: 'scheduled_2',
    text: '📊 Weekly crypto update: Bitcoin holding strong above $60k, Ethereum showing bullish patterns. Always DYOR! What\'s your take on the current market? #Crypto #Bitcoin #Ethereum',
    media: null,
    scheduledFor: '2024-08-13T15:30:00Z',
    status: 'scheduled',
    niche: 'Cryptocurrency',
    createdAt: '2024-08-10T16:45:00Z'
  }
];

// Published posts with analytics
export const mockPublishedPosts = [
  {
    id: 'published_1',
    text: '🔥 Just launched my new project! After 6 months of hard work, it\'s finally live. Check it out and let me know what you think! Link in bio 🚀 #Launch #Startup #Excited',
    media: {
      type: 'image',
      url: 'https://images.unsplash.com/photo-1559136555-9303baea8ebd?w=400&h=300&fit=crop',
      alt: 'Product launch'
    },
    publishedAt: '2024-08-09T10:00:00Z',
    niche: 'Startup Life',
    engagement: {
      likes: 127,
      retweets: 23,
      replies: 15,
      impressions: 2340
    },
    performance: 'high'
  },
  {
    id: 'published_2',
    text: '💭 Unpopular opinion: Most "productivity" advice is just procrastination in disguise. Sometimes you just need to start doing the work. What do you think?',
    media: null,
    publishedAt: '2024-08-08T14:30:00Z',
    niche: 'Productivity',
    engagement: {
      likes: 89,
      retweets: 12,
      replies: 34,
      impressions: 1890
    },
    performance: 'medium'
  },
  {
    id: 'published_3',
    text: '🎯 Marketing tip: Stop selling features, start selling outcomes. People don\'t buy products, they buy better versions of themselves. #Marketing #Sales',
    media: null,
    publishedAt: '2024-08-07T11:15:00Z',
    niche: 'Marketing',
    engagement: {
      likes: 156,
      retweets: 45,
      replies: 8,
      impressions: 3120
    },
    performance: 'high'
  }
];

// Templates for advanced users
export const mockTemplates = [
  {
    id: 'template_1',
    name: 'Crypto Price Alert',
    description: 'Automatically tweet when Bitcoin price changes by more than 5%',
    code: `function generatePost(data) {
  const { symbol, price, change, changePercent } = data;
  const emoji = changePercent > 0 ? '🚀' : '📉';
  const direction = changePercent > 0 ? 'up' : 'down';
  
  return {
    text: \`\${emoji} \${symbol} is \${direction} \${Math.abs(changePercent).toFixed(2)}% to $\${price.toLocaleString()}! 
    
#\${symbol} #Crypto #PriceAlert\`,
    media: null
  };
}`,
    isActive: true,
    lastRun: '2024-08-10T08:00:00Z',
    createdAt: '2024-07-15T10:00:00Z'
  },
  {
    id: 'template_2',
    name: 'Daily Tech News',
    description: 'Curate and share the top tech news of the day',
    code: `function generatePost(data) {
  const { headlines, topStory } = data;
  
  return {
    text: \`📰 Today's top tech stories:

1. \${topStory.title}
2. \${headlines[1].title}
3. \${headlines[2].title}

Which story interests you most? 🤔

#TechNews #Technology\`,
    media: topStory.image || null
  };
}`,
    isActive: false,
    lastRun: '2024-08-09T06:00:00Z',
    createdAt: '2024-07-20T14:30:00Z'
  }
];

// Analytics data
export const mockAnalytics = {
  overview: {
    totalPosts: 45,
    totalEngagement: 2847,
    avgEngagementRate: 4.2,
    followerGrowth: 12.5,
    topPerformingNiche: 'AI & Machine Learning'
  },
  weeklyStats: [
    { day: 'Mon', posts: 3, engagement: 245 },
    { day: 'Tue', posts: 2, engagement: 189 },
    { day: 'Wed', posts: 4, engagement: 312 },
    { day: 'Thu', posts: 3, engagement: 278 },
    { day: 'Fri', posts: 2, engagement: 156 },
    { day: 'Sat', posts: 1, engagement: 89 },
    { day: 'Sun', posts: 2, engagement: 134 }
  ],
  nichePerformance: [
    { niche: 'AI & Machine Learning', posts: 12, avgEngagement: 145 },
    { niche: 'Marketing', posts: 8, avgEngagement: 132 },
    { niche: 'Productivity', posts: 6, avgEngagement: 98 },
    { niche: 'Design', posts: 5, avgEngagement: 87 },
    { niche: 'Startup Life', posts: 4, avgEngagement: 156 }
  ]
};
