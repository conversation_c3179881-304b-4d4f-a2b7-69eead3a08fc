import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Clock, X } from 'lucide-react'
import Card from '../UI/Card'
import Button from '../UI/Button'
import { schedulingApi } from '../../api/mockApi'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants'
import { format } from 'date-fns'

const TimeSlotPicker = ({ 
  selectedDate, 
  onTimeSelect, 
  onClose,
  selectedPost 
}) => {
  const [availableSlots, setAvailableSlots] = useState([])
  const [selectedTime, setSelectedTime] = useState('')
  const [customTime, setCustomTime] = useState('')
  const [isCustomTime, setIsCustomTime] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (selectedDate) {
      loadAvailableSlots()
    }
  }, [selectedDate])

  const loadAvailableSlots = async () => {
    setLoading(true)
    try {
      const slots = await schedulingApi.getAvailableSlots(selectedDate)
      setAvailableSlots(slots)
    } catch (error) {
      console.error('Failed to load available slots:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleTimeSelect = (time) => {
    setSelectedTime(time)
    setIsCustomTime(false)
    setCustomTime('')
  }

  const handleCustomTimeToggle = () => {
    setIsCustomTime(!isCustomTime)
    setSelectedTime('')
  }

  const handleSchedule = () => {
    const timeToUse = isCustomTime ? customTime : selectedTime
    if (timeToUse && selectedDate) {
      const [hours, minutes] = timeToUse.split(':')
      const scheduledDateTime = new Date(selectedDate)
      scheduledDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0)
      
      onTimeSelect(scheduledDateTime)
    }
  }

  const isValidTime = () => {
    if (isCustomTime) {
      return customTime && /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(customTime)
    }
    return selectedTime !== ''
  }

  const getOptimalTimes = () => {
    // Return suggested optimal posting times
    return ['09:00', '12:00', '15:00', '18:00', '21:00']
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="time-picker-overlay"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="time-picker-modal"
          onClick={(e) => e.stopPropagation()}
        >
          <Card className="time-picker-content">
            {/* Header */}
            <div className="modal-header">
              <div className="header-info">
                <h3>Schedule Post</h3>
                <p>Select a time for {format(selectedDate, 'EEEE, MMMM d, yyyy')}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                icon={<X size={16} />}
                onClick={onClose}
              />
            </div>

            {/* Post Preview */}
            {selectedPost && (
              <div className="post-preview">
                <h4>Post to schedule:</h4>
                <div className="preview-content">
                  <p>{selectedPost.text.substring(0, 100)}...</p>
                  {selectedPost.media && (
                    <div className="media-indicator">
                      📷 Media attached
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Time Selection */}
            <div className="time-selection">
              <div className="section-header">
                <Clock size={20} />
                <h4>Choose Time</h4>
              </div>

              {loading ? (
                <div className="loading-slots">
                  <div className="loading-spinner" />
                  <p>Loading available times...</p>
                </div>
              ) : (
                <>
                  {/* Optimal Times */}
                  <div className="optimal-times">
                    <h5>Recommended Times</h5>
                    <div className="time-grid">
                      {getOptimalTimes().map((time) => (
                        <button
                          key={time}
                          className={`time-slot optimal ${selectedTime === time ? 'selected' : ''}`}
                          onClick={() => handleTimeSelect(time)}
                          disabled={isCustomTime}
                        >
                          <span className="time">{time}</span>
                          <span className="label">Optimal</span>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Available Times */}
                  <div className="available-times">
                    <h5>Available Times</h5>
                    <div className="time-grid">
                      {availableSlots.filter(slot => !getOptimalTimes().includes(slot)).map((time) => (
                        <button
                          key={time}
                          className={`time-slot ${selectedTime === time ? 'selected' : ''}`}
                          onClick={() => handleTimeSelect(time)}
                          disabled={isCustomTime}
                        >
                          {time}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Custom Time */}
                  <div className="custom-time">
                    <div className="custom-header">
                      <h5>Custom Time</h5>
                      <button
                        className={`toggle-custom ${isCustomTime ? 'active' : ''}`}
                        onClick={handleCustomTimeToggle}
                      >
                        {isCustomTime ? 'Use Preset' : 'Custom'}
                      </button>
                    </div>
                    
                    {isCustomTime && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="custom-input"
                      >
                        <input
                          type="time"
                          value={customTime}
                          onChange={(e) => setCustomTime(e.target.value)}
                          className="time-input"
                        />
                      </motion.div>
                    )}
                  </div>
                </>
              )}
            </div>

            {/* Actions */}
            <div className="modal-actions">
              <Button
                variant="outline"
                onClick={onClose}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSchedule}
                disabled={!isValidTime() || loading}
              >
                Schedule Post
              </Button>
            </div>
          </Card>
        </motion.div>

        <style jsx>{`
          .time-picker-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: ${SPACING.lg};
          }

          .time-picker-modal {
            width: 100%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
          }

          .time-picker-content {
            padding: 0;
          }

          .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: ${SPACING.xl};
            border-bottom: 1px solid ${COLORS.gray200};
          }

          .header-info h3 {
            font-size: ${TYPOGRAPHY.fontSize.xl};
            font-weight: ${TYPOGRAPHY.fontWeight.semibold};
            color: ${COLORS.textPrimary};
            margin: 0 0 ${SPACING.xs} 0;
          }

          .header-info p {
            color: ${COLORS.textSecondary};
            margin: 0;
          }

          .post-preview {
            padding: ${SPACING.lg} ${SPACING.xl};
            background: ${COLORS.gray50};
            border-bottom: 1px solid ${COLORS.gray200};
          }

          .post-preview h4 {
            font-size: ${TYPOGRAPHY.fontSize.sm};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
            color: ${COLORS.textSecondary};
            margin: 0 0 ${SPACING.sm} 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .preview-content {
            background: ${COLORS.background};
            padding: ${SPACING.md};
            border-radius: ${BORDER_RADIUS.lg};
            border: 1px solid ${COLORS.gray200};
          }

          .preview-content p {
            margin: 0 0 ${SPACING.sm} 0;
            color: ${COLORS.textPrimary};
          }

          .media-indicator {
            font-size: ${TYPOGRAPHY.fontSize.xs};
            color: ${COLORS.textMuted};
          }

          .time-selection {
            padding: ${SPACING.xl};
          }

          .section-header {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
            margin-bottom: ${SPACING.lg};
          }

          .section-header h4 {
            font-size: ${TYPOGRAPHY.fontSize.lg};
            font-weight: ${TYPOGRAPHY.fontWeight.semibold};
            color: ${COLORS.textPrimary};
            margin: 0;
          }

          .loading-slots {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: ${SPACING.md};
            padding: ${SPACING['2xl']};
          }

          .loading-spinner {
            width: 24px;
            height: 24px;
            border: 2px solid ${COLORS.gray200};
            border-top: 2px solid ${COLORS.primary};
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          .optimal-times,
          .available-times,
          .custom-time {
            margin-bottom: ${SPACING.lg};
          }

          .optimal-times h5,
          .available-times h5,
          .custom-time h5 {
            font-size: ${TYPOGRAPHY.fontSize.sm};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
            color: ${COLORS.textSecondary};
            margin: 0 0 ${SPACING.md} 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }

          .time-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: ${SPACING.sm};
          }

          .time-slot {
            padding: ${SPACING.md};
            border: 1px solid ${COLORS.gray300};
            border-radius: ${BORDER_RADIUS.lg};
            background: ${COLORS.background};
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
            display: flex;
            flex-direction: column;
            gap: ${SPACING.xs};
          }

          .time-slot:hover:not(:disabled) {
            border-color: ${COLORS.primary};
            background: ${COLORS.primary}10;
          }

          .time-slot.selected {
            border-color: ${COLORS.primary};
            background: ${COLORS.primary};
            color: white;
          }

          .time-slot:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          .time-slot.optimal {
            border-color: ${COLORS.success};
          }

          .time-slot.optimal:hover:not(:disabled) {
            border-color: ${COLORS.success};
            background: ${COLORS.success}10;
          }

          .time-slot.optimal.selected {
            background: ${COLORS.success};
          }

          .time-slot .time {
            font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          }

          .time-slot .label {
            font-size: ${TYPOGRAPHY.fontSize.xs};
            opacity: 0.8;
          }

          .custom-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: ${SPACING.md};
          }

          .toggle-custom {
            padding: ${SPACING.xs} ${SPACING.sm};
            border: 1px solid ${COLORS.gray300};
            border-radius: ${BORDER_RADIUS.md};
            background: ${COLORS.background};
            color: ${COLORS.textSecondary};
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: ${TYPOGRAPHY.fontSize.sm};
          }

          .toggle-custom:hover {
            border-color: ${COLORS.primary};
            color: ${COLORS.primary};
          }

          .toggle-custom.active {
            background: ${COLORS.primary};
            border-color: ${COLORS.primary};
            color: white;
          }

          .custom-input {
            overflow: hidden;
          }

          .time-input {
            width: 100%;
            padding: ${SPACING.md};
            border: 1px solid ${COLORS.gray300};
            border-radius: ${BORDER_RADIUS.lg};
            font-size: ${TYPOGRAPHY.fontSize.base};
          }

          .time-input:focus {
            outline: none;
            border-color: ${COLORS.primary};
            box-shadow: 0 0 0 3px ${COLORS.primary}20;
          }

          .modal-actions {
            display: flex;
            justify-content: flex-end;
            gap: ${SPACING.md};
            padding: ${SPACING.xl};
            border-top: 1px solid ${COLORS.gray200};
          }

          @media (max-width: 640px) {
            .time-picker-overlay {
              padding: ${SPACING.md};
            }

            .time-grid {
              grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
            }

            .modal-header,
            .time-selection,
            .modal-actions {
              padding: ${SPACING.lg};
            }
          }
        `}</style>
      </motion.div>
    </AnimatePresence>
  )
}

export default TimeSlotPicker
