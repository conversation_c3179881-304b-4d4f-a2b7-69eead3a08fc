import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Calendar, 
  Clock, 
  Edit, 
  Trash2, 
  MoreH<PERSON>zontal,
  Send,
  Pause
} from 'lucide-react'
import Card from '../UI/Card'
import Button from '../UI/Button'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants'
import { formatRelativeTime, formatDateTime, truncateText } from '../../utils/helpers'

const ScheduledPostItem = ({ 
  post, 
  onEdit, 
  onDelete, 
  onReschedule, 
  onPublishNow,
  onPause 
}) => {
  const [showActions, setShowActions] = useState(false)

  const getStatusColor = () => {
    switch (post.status) {
      case 'scheduled':
        return COLORS.primary
      case 'paused':
        return COLORS.warning
      default:
        return COLORS.gray400
    }
  }

  const getStatusText = () => {
    switch (post.status) {
      case 'scheduled':
        return 'Scheduled'
      case 'paused':
        return 'Paused'
      default:
        return 'Unknown'
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="scheduled-post-item">
        {/* Header */}
        <div className="post-header">
          <div className="schedule-info">
            <div className="schedule-time">
              <Calendar size={16} />
              <span>{formatDateTime(post.scheduledFor)}</span>
            </div>
            <div className="relative-time">
              {formatRelativeTime(post.scheduledFor)}
            </div>
          </div>
          
          <div className="post-status">
            <div 
              className="status-indicator"
              style={{ backgroundColor: getStatusColor() }}
            />
            <span className="status-text">{getStatusText()}</span>
          </div>
        </div>

        {/* Content */}
        <div className="post-content">
          <p className="post-text">
            {truncateText(post.text, 150)}
          </p>
          
          {post.media && (
            <div className="media-preview">
              <img 
                src={post.media.url} 
                alt={post.media.alt}
                className="media-image"
              />
            </div>
          )}
          
          <div className="post-meta">
            <span className="niche-tag">{post.niche}</span>
            <span className="created-time">
              Created {formatRelativeTime(post.createdAt)}
            </span>
          </div>
        </div>

        {/* Actions */}
        <div className="post-actions">
          <div className="primary-actions">
            <Button
              variant="outline"
              size="sm"
              icon={<Edit size={14} />}
              onClick={() => onEdit(post.id)}
            >
              Edit
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              icon={<Clock size={14} />}
              onClick={() => onReschedule(post.id)}
            >
              Reschedule
            </Button>
          </div>

          <div className="secondary-actions">
            <div className="dropdown-container">
              <Button
                variant="ghost"
                size="sm"
                icon={<MoreHorizontal size={14} />}
                onClick={() => setShowActions(!showActions)}
              />
              
              <AnimatePresence>
                {showActions && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    className="actions-dropdown"
                  >
                    <button 
                      className="action-item"
                      onClick={() => {
                        onPublishNow(post.id)
                        setShowActions(false)
                      }}
                    >
                      <Send size={14} />
                      Publish Now
                    </button>
                    
                    <button 
                      className="action-item"
                      onClick={() => {
                        onPause(post.id)
                        setShowActions(false)
                      }}
                    >
                      <Pause size={14} />
                      {post.status === 'paused' ? 'Resume' : 'Pause'}
                    </button>
                    
                    <div className="action-divider" />
                    
                    <button 
                      className="action-item danger"
                      onClick={() => {
                        onDelete(post.id)
                        setShowActions(false)
                      }}
                    >
                      <Trash2 size={14} />
                      Delete
                    </button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
        </div>

        <style jsx>{`
          .scheduled-post-item {
            border-left: 4px solid ${getStatusColor()};
            position: relative;
          }

          .post-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: ${SPACING.lg};
          }

          .schedule-info {
            flex: 1;
          }

          .schedule-time {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
            color: ${COLORS.textPrimary};
            margin-bottom: ${SPACING.xs};
          }

          .relative-time {
            font-size: ${TYPOGRAPHY.fontSize.sm};
            color: ${COLORS.textSecondary};
          }

          .post-status {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
          }

          .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }

          .status-text {
            font-size: ${TYPOGRAPHY.fontSize.sm};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
            color: ${COLORS.textSecondary};
          }

          .post-content {
            margin-bottom: ${SPACING.lg};
          }

          .post-text {
            color: ${COLORS.textPrimary};
            line-height: ${TYPOGRAPHY.lineHeight.relaxed};
            margin-bottom: ${SPACING.md};
          }

          .media-preview {
            margin-bottom: ${SPACING.md};
            border-radius: ${BORDER_RADIUS.lg};
            overflow: hidden;
            border: 1px solid ${COLORS.gray200};
          }

          .media-image {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
          }

          .post-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: ${SPACING.md};
          }

          .niche-tag {
            background: ${COLORS.primary}15;
            color: ${COLORS.primary};
            padding: ${SPACING.xs} ${SPACING.sm};
            border-radius: ${BORDER_RADIUS.md};
            font-size: ${TYPOGRAPHY.fontSize.xs};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
          }

          .created-time {
            font-size: ${TYPOGRAPHY.fontSize.xs};
            color: ${COLORS.textMuted};
          }

          .post-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: ${SPACING.md};
            border-top: 1px solid ${COLORS.gray200};
          }

          .primary-actions {
            display: flex;
            gap: ${SPACING.sm};
          }

          .secondary-actions {
            display: flex;
            align-items: center;
          }

          .dropdown-container {
            position: relative;
          }

          .actions-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: ${COLORS.background};
            border: 1px solid ${COLORS.gray200};
            border-radius: ${BORDER_RADIUS.lg};
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 10;
            min-width: 160px;
            margin-top: ${SPACING.xs};
            overflow: hidden;
          }

          .action-item {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
            width: 100%;
            padding: ${SPACING.md};
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: ${COLORS.textPrimary};
            font-size: ${TYPOGRAPHY.fontSize.sm};
          }

          .action-item:hover {
            background: ${COLORS.gray100};
          }

          .action-item.danger {
            color: ${COLORS.error};
          }

          .action-item.danger:hover {
            background: ${COLORS.error}10;
          }

          .action-divider {
            height: 1px;
            background: ${COLORS.gray200};
            margin: ${SPACING.xs} 0;
          }
        `}</style>
      </Card>
    </motion.div>
  )
}

const ScheduledPostsList = ({ 
  posts, 
  onEdit, 
  onDelete, 
  onReschedule, 
  onPublishNow,
  onPause,
  loading = false 
}) => {
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner" />
        <p>Loading scheduled posts...</p>
        
        <style jsx>{`
          .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: ${SPACING['3xl']};
            gap: ${SPACING.lg};
          }

          .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid ${COLORS.gray200};
            border-top: 3px solid ${COLORS.primary};
            border-radius: 50%;
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    )
  }

  if (posts.length === 0) {
    return (
      <Card className="empty-state">
        <div className="empty-content">
          <Calendar size={48} className="empty-icon" />
          <h3>No scheduled posts</h3>
          <p>Posts you schedule will appear here. Use the calendar to schedule your first post.</p>
        </div>

        <style jsx>{`
          .empty-state {
            text-align: center;
            padding: ${SPACING['3xl']};
          }

          .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: ${SPACING.lg};
          }

          .empty-icon {
            color: ${COLORS.gray400};
          }

          .empty-content h3 {
            font-size: ${TYPOGRAPHY.fontSize.xl};
            font-weight: ${TYPOGRAPHY.fontWeight.semibold};
            color: ${COLORS.textPrimary};
            margin: 0;
          }

          .empty-content p {
            color: ${COLORS.textSecondary};
            margin: 0;
            max-width: 300px;
          }
        `}</style>
      </Card>
    )
  }

  return (
    <div className="scheduled-posts-list">
      <div className="posts-container">
        <AnimatePresence>
          {posts.map((post) => (
            <ScheduledPostItem
              key={post.id}
              post={post}
              onEdit={onEdit}
              onDelete={onDelete}
              onReschedule={onReschedule}
              onPublishNow={onPublishNow}
              onPause={onPause}
            />
          ))}
        </AnimatePresence>
      </div>

      <style jsx>{`
        .scheduled-posts-list {
          width: 100%;
        }

        .posts-container {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.lg};
        }
      `}</style>
    </div>
  )
}

export default ScheduledPostsList
