import { useState, useMemo } from 'react'
import { motion } from 'framer-motion'
import { ChevronLeft, ChevronRight, Plus } from 'lucide-react'
import Button from '../UI/Button'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants'
import { format, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, isToday, addMonths, subMonths } from 'date-fns'

const Calendar = ({ 
  selectedDate, 
  onDateSelect, 
  scheduledPosts = [],
  onCreatePost 
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date())

  const monthStart = startOfMonth(currentMonth)
  const monthEnd = endOfMonth(monthStart)
  const startDate = startOfWeek(monthStart)
  const endDate = endOfWeek(monthEnd)

  const dateFormat = "d"
  const rows = []

  let days = []
  let day = startDate
  let formattedDate = ""

  // Generate calendar grid
  while (day <= endDate) {
    for (let i = 0; i < 7; i++) {
      formattedDate = format(day, dateFormat)
      const cloneDay = day
      
      // Count posts for this day
      const postsForDay = scheduledPosts.filter(post => 
        isSameDay(new Date(post.scheduledFor), day)
      )

      days.push(
        <div
          className={`calendar-day ${
            !isSameMonth(day, monthStart) ? 'disabled' : ''
          } ${
            isSameDay(day, selectedDate) ? 'selected' : ''
          } ${
            isToday(day) ? 'today' : ''
          }`}
          key={day}
          onClick={() => onDateSelect(cloneDay)}
        >
          <span className="day-number">{formattedDate}</span>
          {postsForDay.length > 0 && (
            <div className="post-indicators">
              {postsForDay.slice(0, 3).map((post, index) => (
                <div
                  key={post.id}
                  className="post-dot"
                  title={`${format(new Date(post.scheduledFor), 'HH:mm')} - ${post.text.substring(0, 50)}...`}
                />
              ))}
              {postsForDay.length > 3 && (
                <div className="post-count">+{postsForDay.length - 3}</div>
              )}
            </div>
          )}
          <button
            className="add-post-btn"
            onClick={(e) => {
              e.stopPropagation()
              onCreatePost(cloneDay)
            }}
            title="Schedule post for this day"
          >
            <Plus size={12} />
          </button>
        </div>
      )
      day = addDays(day, 1)
    }
    rows.push(
      <div className="calendar-row" key={day}>
        {days}
      </div>
    )
    days = []
  }

  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1))
  }

  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1))
  }

  const goToToday = () => {
    const today = new Date()
    setCurrentMonth(today)
    onDateSelect(today)
  }

  return (
    <div className="calendar">
      {/* Header */}
      <div className="calendar-header">
        <div className="month-navigation">
          <Button
            variant="ghost"
            size="sm"
            icon={<ChevronLeft size={16} />}
            onClick={prevMonth}
          />
          
          <h2 className="month-title">
            {format(currentMonth, 'MMMM yyyy')}
          </h2>
          
          <Button
            variant="ghost"
            size="sm"
            icon={<ChevronRight size={16} />}
            onClick={nextMonth}
          />
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={goToToday}
        >
          Today
        </Button>
      </div>

      {/* Days of week header */}
      <div className="calendar-weekdays">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="weekday">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="calendar-grid">
        {rows}
      </div>

      <style jsx>{`
        .calendar {
          background: ${COLORS.background};
          border-radius: ${BORDER_RADIUS.xl};
          border: 1px solid ${COLORS.gray200};
          overflow: hidden;
        }

        .calendar-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: ${SPACING.lg};
          border-bottom: 1px solid ${COLORS.gray200};
          background: ${COLORS.gray50};
        }

        .month-navigation {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
        }

        .month-title {
          font-size: ${TYPOGRAPHY.fontSize.xl};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          color: ${COLORS.textPrimary};
          margin: 0;
          min-width: 200px;
          text-align: center;
        }

        .calendar-weekdays {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
          background: ${COLORS.gray100};
        }

        .weekday {
          padding: ${SPACING.md};
          text-align: center;
          font-size: ${TYPOGRAPHY.fontSize.sm};
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          color: ${COLORS.textSecondary};
          border-right: 1px solid ${COLORS.gray200};
        }

        .weekday:last-child {
          border-right: none;
        }

        .calendar-grid {
          display: flex;
          flex-direction: column;
        }

        .calendar-row {
          display: grid;
          grid-template-columns: repeat(7, 1fr);
        }

        .calendar-day {
          min-height: 100px;
          padding: ${SPACING.sm};
          border-right: 1px solid ${COLORS.gray200};
          border-bottom: 1px solid ${COLORS.gray200};
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          display: flex;
          flex-direction: column;
          background: ${COLORS.background};
        }

        .calendar-day:last-child {
          border-right: none;
        }

        .calendar-day:hover {
          background: ${COLORS.gray50};
        }

        .calendar-day.disabled {
          color: ${COLORS.textMuted};
          background: ${COLORS.gray50};
          cursor: not-allowed;
        }

        .calendar-day.disabled:hover {
          background: ${COLORS.gray50};
        }

        .calendar-day.selected {
          background: ${COLORS.primary}10;
          border-color: ${COLORS.primary};
        }

        .calendar-day.today {
          background: ${COLORS.secondary}10;
        }

        .calendar-day.today .day-number {
          background: ${COLORS.secondary};
          color: white;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
        }

        .day-number {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          color: ${COLORS.textPrimary};
          margin-bottom: ${SPACING.xs};
        }

        .post-indicators {
          display: flex;
          flex-wrap: wrap;
          gap: 2px;
          margin-top: auto;
          margin-bottom: ${SPACING.xs};
        }

        .post-dot {
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background: ${COLORS.primary};
        }

        .post-count {
          font-size: 10px;
          color: ${COLORS.textMuted};
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
        }

        .add-post-btn {
          position: absolute;
          top: ${SPACING.xs};
          right: ${SPACING.xs};
          width: 20px;
          height: 20px;
          border-radius: 50%;
          border: none;
          background: ${COLORS.primary}20;
          color: ${COLORS.primary};
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: all 0.2s ease;
        }

        .calendar-day:hover .add-post-btn {
          opacity: 1;
        }

        .add-post-btn:hover {
          background: ${COLORS.primary};
          color: white;
          transform: scale(1.1);
        }

        @media (max-width: 768px) {
          .calendar-day {
            min-height: 80px;
            padding: ${SPACING.xs};
          }

          .month-title {
            font-size: ${TYPOGRAPHY.fontSize.lg};
            min-width: 150px;
          }

          .calendar-header {
            padding: ${SPACING.md};
          }
        }
      `}</style>
    </div>
  )
}

export default Calendar
