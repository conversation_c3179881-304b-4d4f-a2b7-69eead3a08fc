import { useState, useEffect, createContext, useContext } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Toast } from './LoadingStates'
import { SPACING } from '../../utils/constants'

// Notification Context
const NotificationContext = createContext()

export const useNotifications = () => {
  const context = useContext(NotificationContext)
  if (!context) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

// Notification Provider
export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([])

  const addNotification = (message, type = 'info', duration = 5000) => {
    const id = Date.now() + Math.random()
    const notification = { id, message, type, duration }
    
    setNotifications(prev => [...prev, notification])

    if (duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, duration)
    }

    return id
  }

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id))
  }

  const clearAll = () => {
    setNotifications([])
  }

  // Convenience methods
  const success = (message, duration) => addNotification(message, 'success', duration)
  const error = (message, duration) => addNotification(message, 'error', duration)
  const warning = (message, duration) => addNotification(message, 'warning', duration)
  const info = (message, duration) => addNotification(message, 'info', duration)

  const value = {
    notifications,
    addNotification,
    removeNotification,
    clearAll,
    success,
    error,
    warning,
    info
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <NotificationContainer notifications={notifications} onRemove={removeNotification} />
    </NotificationContext.Provider>
  )
}

// Notification Container
const NotificationContainer = ({ notifications, onRemove }) => {
  return (
    <div className="notification-container">
      <AnimatePresence>
        {notifications.map((notification) => (
          <Toast
            key={notification.id}
            message={notification.message}
            type={notification.type}
            onClose={() => onRemove(notification.id)}
          />
        ))}
      </AnimatePresence>

      <style jsx>{`
        .notification-container {
          position: fixed;
          top: ${SPACING.xl};
          right: ${SPACING.xl};
          z-index: 1000;
          display: flex;
          flex-direction: column;
          gap: ${SPACING.md};
          pointer-events: none;
        }

        .notification-container > :global(*) {
          pointer-events: auto;
        }

        @media (max-width: 768px) {
          .notification-container {
            top: ${SPACING.lg};
            right: ${SPACING.lg};
            left: ${SPACING.lg};
          }
        }
      `}</style>
    </div>
  )
}

// Hook for easy notification usage
export const useToast = () => {
  const { success, error, warning, info } = useNotifications()
  
  return {
    success: (message) => success(message),
    error: (message) => error(message),
    warning: (message) => warning(message),
    info: (message) => info(message)
  }
}
