import { motion } from 'framer-motion'
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../utils/constants'

const Card = ({ 
  children, 
  className = '', 
  padding = 'lg',
  hover = false,
  onClick,
  ...props 
}) => {
  const paddingMap = {
    sm: SPACING.md,
    md: SPACING.lg,
    lg: SPACING.xl,
    xl: SPACING['2xl']
  }

  const cardProps = onClick ? {
    whileHover: { y: -2 },
    whileTap: { y: 0 },
    transition: { duration: 0.2 }
  } : {}

  return (
    <motion.div
      className={`card ${className}`}
      onClick={onClick}
      {...cardProps}
      {...props}
    >
      {children}
      
      <style jsx>{`
        .card {
          background: ${COLORS.background};
          border-radius: ${BORDER_RADIUS.xl};
          box-shadow: ${SHADOWS.card};
          padding: ${paddingMap[padding]};
          border: 1px solid ${COLORS.gray200};
          transition: all 0.2s ease;
          ${onClick ? 'cursor: pointer;' : ''}
        }

        .card:hover {
          ${hover || onClick ? `
            box-shadow: ${SHADOWS.lg};
            border-color: ${COLORS.gray300};
          ` : ''}
        }
      `}</style>
    </motion.div>
  )
}

export default Card
