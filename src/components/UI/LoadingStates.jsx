import { motion } from 'framer-motion'
import { COLORS, SPACING, TYPO<PERSON>APHY } from '../../utils/constants'

// Skeleton loader for cards
export const SkeletonCard = ({ lines = 3, showImage = false }) => {
  return (
    <div className="skeleton-card">
      {showImage && <div className="skeleton-image" />}
      <div className="skeleton-content">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`skeleton-line ${index === lines - 1 ? 'short' : ''}`}
          />
        ))}
      </div>

      <style jsx>{`
        .skeleton-card {
          background: ${COLORS.background};
          border-radius: 12px;
          padding: ${SPACING.lg};
          border: 1px solid ${COLORS.gray200};
          animation: pulse 2s infinite;
        }

        .skeleton-image {
          width: 100%;
          height: 120px;
          background: ${COLORS.gray200};
          border-radius: 8px;
          margin-bottom: ${SPACING.md};
          animation: shimmer 2s infinite;
        }

        .skeleton-content {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.sm};
        }

        .skeleton-line {
          height: 16px;
          background: ${COLORS.gray200};
          border-radius: 4px;
          animation: shimmer 2s infinite;
        }

        .skeleton-line.short {
          width: 60%;
        }

        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
        }

        @keyframes shimmer {
          0% {
            background-position: -200px 0;
          }
          100% {
            background-position: calc(200px + 100%) 0;
          }
        }

        .skeleton-line,
        .skeleton-image {
          background: linear-gradient(
            90deg,
            ${COLORS.gray200} 0px,
            ${COLORS.gray100} 40px,
            ${COLORS.gray200} 80px
          );
          background-size: 200px;
        }
      `}</style>
    </div>
  )
}

// Floating action button with animation
export const FloatingActionButton = ({ icon, onClick, tooltip }) => {
  return (
    <motion.button
      className="fab"
      onClick={onClick}
      whileHover={{ scale: 1.1, y: -2 }}
      whileTap={{ scale: 0.95 }}
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      transition={{ type: "spring", stiffness: 260, damping: 20 }}
      title={tooltip}
    >
      {icon}
      
      <style jsx>{`
        .fab {
          position: fixed;
          bottom: ${SPACING['2xl']};
          right: ${SPACING['2xl']};
          width: 56px;
          height: 56px;
          border-radius: 50%;
          background: ${COLORS.primary};
          color: white;
          border: none;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
          z-index: 100;
          transition: box-shadow 0.3s ease;
        }

        .fab:hover {
          box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
          .fab {
            bottom: ${SPACING.xl};
            right: ${SPACING.xl};
            width: 48px;
            height: 48px;
          }
        }
      `}</style>
    </motion.button>
  )
}

// Progress indicator
export const ProgressIndicator = ({ progress, label, color = COLORS.primary }) => {
  return (
    <div className="progress-indicator">
      {label && <div className="progress-label">{label}</div>}
      <div className="progress-track">
        <motion.div
          className="progress-fill"
          initial={{ width: 0 }}
          animate={{ width: `${progress}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
      <div className="progress-text">{progress}%</div>

      <style jsx>{`
        .progress-indicator {
          width: 100%;
        }

        .progress-label {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          color: ${COLORS.textSecondary};
          margin-bottom: ${SPACING.sm};
        }

        .progress-track {
          width: 100%;
          height: 8px;
          background: ${COLORS.gray200};
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: ${SPACING.xs};
        }

        .progress-fill {
          height: 100%;
          background: ${color};
          border-radius: 4px;
        }

        .progress-text {
          font-size: ${TYPOGRAPHY.fontSize.xs};
          color: ${COLORS.textMuted};
          text-align: right;
        }
      `}</style>
    </div>
  )
}

// Notification toast
export const Toast = ({ message, type = 'info', onClose }) => {
  const getToastColor = () => {
    switch (type) {
      case 'success':
        return COLORS.success
      case 'error':
        return COLORS.error
      case 'warning':
        return COLORS.warning
      default:
        return COLORS.info
    }
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return '✅'
      case 'error':
        return '❌'
      case 'warning':
        return '⚠️'
      default:
        return 'ℹ️'
    }
  }

  return (
    <motion.div
      className="toast"
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: 50, scale: 0.9 }}
      transition={{ duration: 0.3 }}
    >
      <div className="toast-icon">{getIcon()}</div>
      <div className="toast-message">{message}</div>
      <button className="toast-close" onClick={onClose}>
        ×
      </button>

      <style jsx>{`
        .toast {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
          background: ${COLORS.background};
          border: 1px solid ${getToastColor()};
          border-left: 4px solid ${getToastColor()};
          border-radius: ${SPACING.md};
          padding: ${SPACING.md} ${SPACING.lg};
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
          min-width: 300px;
          max-width: 500px;
        }

        .toast-icon {
          font-size: 18px;
          flex-shrink: 0;
        }

        .toast-message {
          flex: 1;
          color: ${COLORS.textPrimary};
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .toast-close {
          background: none;
          border: none;
          font-size: 20px;
          color: ${COLORS.textMuted};
          cursor: pointer;
          padding: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s ease;
        }

        .toast-close:hover {
          background: ${COLORS.gray100};
          color: ${COLORS.textPrimary};
        }
      `}</style>
    </motion.div>
  )
}

// Loading dots animation
export const LoadingDots = ({ size = 'md' }) => {
  const sizes = {
    sm: '4px',
    md: '6px',
    lg: '8px'
  }

  return (
    <div className="loading-dots">
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className="dot"
          animate={{
            y: [0, -10, 0],
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 0.6,
            repeat: Infinity,
            delay: index * 0.1
          }}
        />
      ))}

      <style jsx>{`
        .loading-dots {
          display: flex;
          gap: 4px;
          align-items: center;
          justify-content: center;
        }

        .dot {
          width: ${sizes[size]};
          height: ${sizes[size]};
          background: ${COLORS.primary};
          border-radius: 50%;
        }
      `}</style>
    </div>
  )
}

// Pulse animation for live indicators
export const PulseIndicator = ({ color = COLORS.success, size = 8 }) => {
  return (
    <div className="pulse-container">
      <div className="pulse-dot" />
      <div className="pulse-ring" />

      <style jsx>{`
        .pulse-container {
          position: relative;
          width: ${size}px;
          height: ${size}px;
        }

        .pulse-dot {
          width: ${size}px;
          height: ${size}px;
          background: ${color};
          border-radius: 50%;
          position: absolute;
          top: 0;
          left: 0;
        }

        .pulse-ring {
          width: ${size}px;
          height: ${size}px;
          border: 2px solid ${color};
          border-radius: 50%;
          position: absolute;
          top: 0;
          left: 0;
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0% {
            transform: scale(1);
            opacity: 1;
          }
          100% {
            transform: scale(2);
            opacity: 0;
          }
        }
      `}</style>
    </div>
  )
}
