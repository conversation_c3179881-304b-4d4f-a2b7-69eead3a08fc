import { motion } from 'framer-motion'
import { COLORS, SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../../utils/constants'

const Button = ({
  children,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  icon,
  onClick,
  className = '',
  ...props
}) => {
  const variants = {
    primary: {
      background: COLORS.primary,
      color: 'white',
      hoverBackground: `${COLORS.primary}dd`,
    },
    secondary: {
      background: COLORS.gray100,
      color: COLORS.textPrimary,
      hoverBackground: COLORS.gray200,
    },
    outline: {
      background: 'transparent',
      color: COLORS.primary,
      border: `1px solid ${COLORS.primary}`,
      hoverBackground: `${COLORS.primary}10`,
    },
    ghost: {
      background: 'transparent',
      color: COLORS.textSecondary,
      hoverBackground: COLORS.gray100,
    },
    danger: {
      background: COLORS.error,
      color: 'white',
      hoverBackground: `${COLORS.error}dd`,
    }
  }

  const sizes = {
    sm: {
      padding: `${SPACING.sm} ${SPACING.md}`,
      fontSize: TYPOGRAPHY.fontSize.sm,
    },
    md: {
      padding: `${SPACING.md} ${SPACING.lg}`,
      fontSize: TYPOGRAPHY.fontSize.base,
    },
    lg: {
      padding: `${SPACING.lg} ${SPACING.xl}`,
      fontSize: TYPOGRAPHY.fontSize.lg,
    }
  }

  const currentVariant = variants[variant]
  const currentSize = sizes[size]

  return (
    <motion.button
      className={`button ${className}`}
      onClick={onClick}
      disabled={disabled || loading}
      whileHover={!disabled && !loading ? { y: -1 } : {}}
      whileTap={!disabled && !loading ? { y: 0 } : {}}
      transition={{ duration: 0.2 }}
      {...props}
    >
      {loading && (
        <div className="loading-spinner" />
      )}
      {icon && !loading && (
        <span className="button-icon">{icon}</span>
      )}
      <span className={loading ? 'loading-text' : ''}>{children}</span>

      <style jsx>{`
        .button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: ${SPACING.sm};
          background: ${currentVariant.background};
          color: ${currentVariant.color};
          border: ${currentVariant.border || 'none'};
          border-radius: ${BORDER_RADIUS.lg};
          padding: ${currentSize.padding};
          font-size: ${currentSize.fontSize};
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          font-family: ${TYPOGRAPHY.fontFamily.primary};
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          white-space: nowrap;
        }

        .button:hover:not(:disabled) {
          background: ${currentVariant.hoverBackground};
        }

        .button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
        }

        .button:focus {
          outline: 2px solid ${COLORS.primary};
          outline-offset: 2px;
        }

        .button-icon {
          display: flex;
          align-items: center;
        }

        .loading-spinner {
          width: 16px;
          height: 16px;
          border: 2px solid transparent;
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        .loading-text {
          opacity: 0.7;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </motion.button>
  )
}

export default Button
