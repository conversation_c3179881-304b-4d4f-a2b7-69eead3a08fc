import { useState } from 'react'
import { NavLink, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  Home, 
  Sparkles, 
  Code, 
  Calendar, 
  Settings,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { NAVIGATION_ITEMS, COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../utils/constants'

const iconMap = {
  Home,
  Sparkles,
  Code,
  Calendar,
  Settings
}

const Sidebar = ({ isCollapsed, isMobileMenuOpen, onToggle, onMobileToggle }) => {
  const location = useLocation()

  const sidebarVariants = {
    expanded: { width: 280 },
    collapsed: { width: 80 }
  }

  const mobileSidebarVariants = {
    open: { x: 0 },
    closed: { x: -280 }
  }

  return (
    <>
      {/* Desktop Sidebar */}
      <motion.aside
        variants={sidebarVariants}
        animate={isCollapsed ? 'collapsed' : 'expanded'}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="sidebar desktop-sidebar"
      >
        <div className="sidebar-content">
          {/* Logo */}
          <div className="logo-section">
            <motion.div
              initial={false}
              animate={{ opacity: isCollapsed ? 0 : 1 }}
              transition={{ duration: 0.2 }}
              className="logo"
            >
              {!isCollapsed && (
                <h1 className="logo-text">TweetCrafter</h1>
              )}
            </motion.div>
            
            <button
              onClick={onToggle}
              className="collapse-button"
              aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            >
              {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
            </button>
          </div>

          {/* Navigation */}
          <nav className="navigation">
            <ul className="nav-list">
              {NAVIGATION_ITEMS.map((item) => {
                const Icon = iconMap[item.icon]
                const isActive = location.pathname === item.path
                
                return (
                  <li key={item.id} className="nav-item">
                    <NavLink
                      to={item.path}
                      className={`nav-link ${isActive ? 'active' : ''}`}
                      title={isCollapsed ? item.name : undefined}
                    >
                      <Icon size={20} className="nav-icon" />
                      <motion.span
                        initial={false}
                        animate={{ 
                          opacity: isCollapsed ? 0 : 1,
                          width: isCollapsed ? 0 : 'auto'
                        }}
                        transition={{ duration: 0.2 }}
                        className="nav-text"
                      >
                        {item.name}
                      </motion.span>
                    </NavLink>
                  </li>
                )
              })}
            </ul>
          </nav>
        </div>
      </motion.aside>

      {/* Mobile Sidebar */}
      <motion.aside
        variants={mobileSidebarVariants}
        animate={isMobileMenuOpen ? 'open' : 'closed'}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
        className="sidebar mobile-sidebar"
      >
        <div className="sidebar-content">
          {/* Logo */}
          <div className="logo-section mobile-logo">
            <h1 className="logo-text">TweetCrafter</h1>
          </div>

          {/* Navigation */}
          <nav className="navigation">
            <ul className="nav-list">
              {NAVIGATION_ITEMS.map((item) => {
                const Icon = iconMap[item.icon]
                const isActive = location.pathname === item.path
                
                return (
                  <li key={item.id} className="nav-item">
                    <NavLink
                      to={item.path}
                      className={`nav-link ${isActive ? 'active' : ''}`}
                      onClick={onMobileToggle}
                    >
                      <Icon size={20} className="nav-icon" />
                      <span className="nav-text">{item.name}</span>
                    </NavLink>
                  </li>
                )
              })}
            </ul>
          </nav>
        </div>
      </motion.aside>

      <style jsx>{`
        .sidebar {
          background-color: ${COLORS.background};
          border-right: 1px solid ${COLORS.gray200};
          box-shadow: ${SHADOWS.md};
          z-index: 50;
        }

        .desktop-sidebar {
          position: fixed;
          top: 0;
          left: 0;
          height: 100vh;
          display: flex;
          flex-direction: column;
        }

        .mobile-sidebar {
          position: fixed;
          top: 0;
          left: 0;
          height: 100vh;
          width: 280px;
          display: none;
        }

        .sidebar-content {
          display: flex;
          flex-direction: column;
          height: 100%;
          padding: ${SPACING.lg};
        }

        .logo-section {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: ${SPACING.xl};
          padding-bottom: ${SPACING.lg};
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .mobile-logo {
          justify-content: center;
        }

        .logo-text {
          font-size: 1.5rem;
          font-weight: 700;
          color: ${COLORS.primary};
          margin: 0;
        }

        .collapse-button {
          background: none;
          border: none;
          padding: ${SPACING.sm};
          border-radius: ${BORDER_RADIUS.md};
          color: ${COLORS.gray600};
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .collapse-button:hover {
          background-color: ${COLORS.gray100};
          color: ${COLORS.gray800};
        }

        .navigation {
          flex: 1;
        }

        .nav-list {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .nav-item {
          margin-bottom: ${SPACING.sm};
        }

        .nav-link {
          display: flex;
          align-items: center;
          padding: ${SPACING.md};
          border-radius: ${BORDER_RADIUS.lg};
          color: ${COLORS.gray600};
          text-decoration: none;
          transition: all 0.2s ease;
          position: relative;
          overflow: hidden;
        }

        .nav-link:hover {
          background-color: ${COLORS.gray100};
          color: ${COLORS.gray800};
        }

        .nav-link.active {
          background-color: ${COLORS.primary};
          color: white;
        }

        .nav-icon {
          flex-shrink: 0;
          margin-right: ${SPACING.md};
        }

        .nav-text {
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
        }

        /* Mobile styles */
        @media (max-width: 768px) {
          .desktop-sidebar {
            display: none;
          }

          .mobile-sidebar {
            display: flex;
          }

          .collapse-button {
            display: none;
          }
        }
      `}</style>
    </>
  )
}

export default Sidebar
