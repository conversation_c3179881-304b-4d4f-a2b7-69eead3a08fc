import { useState } from 'react'
import { motion } from 'framer-motion'
import Sidebar from './Sidebar'
import Header from './Header'
import { COLORS, SPACING } from '../../utils/constants'

const Layout = ({ children }) => {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const toggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed)
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <div className="layout">
      {/* Sidebar */}
      <Sidebar 
        isCollapsed={isSidebarCollapsed}
        isMobileMenuOpen={isMobileMenuOpen}
        onToggle={toggleSidebar}
        onMobileToggle={toggleMobileMenu}
      />
      
      {/* Mobile overlay */}
      {isMobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="mobile-overlay"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Main content area */}
      <div className={`main-content ${isSidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
        <Header 
          onMobileMenuToggle={toggleMobileMenu}
          isMobileMenuOpen={isMobileMenuOpen}
        />
        
        <main className="content">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="content-wrapper"
          >
            {children}
          </motion.div>
        </main>
      </div>

      <style jsx>{`
        .layout {
          display: flex;
          min-height: 100vh;
          background-color: ${COLORS.backgroundSecondary};
        }

        .mobile-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          z-index: 40;
          display: none;
        }

        .main-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          margin-left: 280px;
          transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-collapsed {
          margin-left: 80px;
        }

        .content {
          flex: 1;
          padding: ${SPACING.lg};
          overflow-y: auto;
        }

        .content-wrapper {
          max-width: 1200px;
          margin: 0 auto;
          width: 100%;
        }

        /* Mobile styles */
        @media (max-width: 768px) {
          .main-content {
            margin-left: 0;
          }

          .main-content.sidebar-collapsed {
            margin-left: 0;
          }

          .mobile-overlay {
            display: block;
          }

          .content {
            padding: ${SPACING.md};
          }
        }

        /* Tablet styles */
        @media (max-width: 1024px) and (min-width: 769px) {
          .content {
            padding: ${SPACING.lg} ${SPACING.md};
          }
        }
      `}</style>
    </div>
  )
}

export default Layout
