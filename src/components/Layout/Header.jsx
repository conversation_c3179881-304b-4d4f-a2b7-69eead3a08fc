import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON>u, 
  Bell, 
  User, 
  Settings, 
  LogOut,
  ChevronDown
} from 'lucide-react'
import { mockUser } from '../../data/mockData'
import { COLORS, SPACING, BORDER_RADIUS, SHADOWS } from '../../utils/constants'

const Header = ({ onMobileMenuToggle, isMobileMenuOpen }) => {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false)
  const [notifications] = useState([
    { id: 1, message: 'Post scheduled successfully', time: '2 min ago', unread: true },
    { id: 2, message: 'Template deployed', time: '1 hour ago', unread: true },
    { id: 3, message: 'Weekly analytics ready', time: '3 hours ago', unread: false }
  ])
  
  const profileMenuRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target)) {
        setIsProfileMenuOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const unreadCount = notifications.filter(n => n.unread).length

  return (
    <header className="header">
      <div className="header-content">
        {/* Mobile menu button */}
        <button
          className="mobile-menu-button"
          onClick={onMobileMenuToggle}
          aria-label="Toggle mobile menu"
        >
          <Menu size={24} />
        </button>

        {/* Page title - will be dynamic based on current route */}
        <div className="page-title">
          <h1>Dashboard</h1>
        </div>

        {/* Header actions */}
        <div className="header-actions">
          {/* Notifications */}
          <div className="notification-wrapper">
            <button className="notification-button" aria-label="Notifications">
              <Bell size={20} />
              {unreadCount > 0 && (
                <span className="notification-badge">{unreadCount}</span>
              )}
            </button>
          </div>

          {/* Profile menu */}
          <div className="profile-menu" ref={profileMenuRef}>
            <button
              className="profile-button"
              onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              aria-label="Profile menu"
            >
              <img
                src={mockUser.avatar}
                alt={mockUser.name}
                className="profile-avatar"
              />
              <span className="profile-name">{mockUser.name}</span>
              <ChevronDown 
                size={16} 
                className={`chevron ${isProfileMenuOpen ? 'rotated' : ''}`}
              />
            </button>

            <AnimatePresence>
              {isProfileMenuOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className="profile-dropdown"
                >
                  <div className="dropdown-header">
                    <img
                      src={mockUser.avatar}
                      alt={mockUser.name}
                      className="dropdown-avatar"
                    />
                    <div className="dropdown-user-info">
                      <div className="dropdown-name">{mockUser.name}</div>
                      <div className="dropdown-email">{mockUser.email}</div>
                    </div>
                  </div>
                  
                  <div className="dropdown-divider" />
                  
                  <div className="dropdown-menu">
                    <button className="dropdown-item">
                      <User size={16} />
                      <span>Profile</span>
                    </button>
                    <button className="dropdown-item">
                      <Settings size={16} />
                      <span>Settings</span>
                    </button>
                    <div className="dropdown-divider" />
                    <button className="dropdown-item logout">
                      <LogOut size={16} />
                      <span>Sign out</span>
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>

      <style jsx>{`
        .header {
          background-color: ${COLORS.background};
          border-bottom: 1px solid ${COLORS.gray200};
          padding: 0 ${SPACING.lg};
          height: 64px;
          display: flex;
          align-items: center;
          position: sticky;
          top: 0;
          z-index: 30;
        }

        .header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
        }

        .mobile-menu-button {
          display: none;
          background: none;
          border: none;
          padding: ${SPACING.sm};
          border-radius: ${BORDER_RADIUS.md};
          color: ${COLORS.gray600};
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .mobile-menu-button:hover {
          background-color: ${COLORS.gray100};
          color: ${COLORS.gray800};
        }

        .page-title h1 {
          font-size: 1.5rem;
          font-weight: 600;
          color: ${COLORS.textPrimary};
          margin: 0;
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
        }

        .notification-wrapper {
          position: relative;
        }

        .notification-button {
          background: none;
          border: none;
          padding: ${SPACING.sm};
          border-radius: ${BORDER_RADIUS.md};
          color: ${COLORS.gray600};
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
        }

        .notification-button:hover {
          background-color: ${COLORS.gray100};
          color: ${COLORS.gray800};
        }

        .notification-badge {
          position: absolute;
          top: 2px;
          right: 2px;
          background-color: ${COLORS.error};
          color: white;
          font-size: 0.75rem;
          font-weight: 600;
          padding: 2px 6px;
          border-radius: ${BORDER_RADIUS.full};
          min-width: 18px;
          height: 18px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .profile-menu {
          position: relative;
        }

        .profile-button {
          display: flex;
          align-items: center;
          gap: ${SPACING.sm};
          background: none;
          border: none;
          padding: ${SPACING.sm};
          border-radius: ${BORDER_RADIUS.lg};
          cursor: pointer;
          transition: all 0.2s ease;
        }

        .profile-button:hover {
          background-color: ${COLORS.gray100};
        }

        .profile-avatar {
          width: 32px;
          height: 32px;
          border-radius: ${BORDER_RADIUS.full};
          object-fit: cover;
        }

        .profile-name {
          font-weight: 500;
          color: ${COLORS.textPrimary};
        }

        .chevron {
          transition: transform 0.2s ease;
          color: ${COLORS.gray500};
        }

        .chevron.rotated {
          transform: rotate(180deg);
        }

        .profile-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          margin-top: ${SPACING.sm};
          background-color: ${COLORS.background};
          border: 1px solid ${COLORS.gray200};
          border-radius: ${BORDER_RADIUS.lg};
          box-shadow: ${SHADOWS.lg};
          min-width: 240px;
          overflow: hidden;
        }

        .dropdown-header {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
          padding: ${SPACING.lg};
          background-color: ${COLORS.gray50};
        }

        .dropdown-avatar {
          width: 40px;
          height: 40px;
          border-radius: ${BORDER_RADIUS.full};
          object-fit: cover;
        }

        .dropdown-name {
          font-weight: 600;
          color: ${COLORS.textPrimary};
        }

        .dropdown-email {
          font-size: 0.875rem;
          color: ${COLORS.textSecondary};
        }

        .dropdown-divider {
          height: 1px;
          background-color: ${COLORS.gray200};
        }

        .dropdown-menu {
          padding: ${SPACING.sm};
        }

        .dropdown-item {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
          width: 100%;
          padding: ${SPACING.md};
          background: none;
          border: none;
          border-radius: ${BORDER_RADIUS.md};
          color: ${COLORS.textPrimary};
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: left;
        }

        .dropdown-item:hover {
          background-color: ${COLORS.gray100};
        }

        .dropdown-item.logout {
          color: ${COLORS.error};
        }

        .dropdown-item.logout:hover {
          background-color: ${COLORS.error};
          color: white;
        }

        /* Mobile styles */
        @media (max-width: 768px) {
          .header {
            padding: 0 ${SPACING.md};
          }

          .mobile-menu-button {
            display: flex;
          }

          .profile-name {
            display: none;
          }

          .page-title h1 {
            font-size: 1.25rem;
          }
        }
      `}</style>
    </header>
  )
}

export default Header
