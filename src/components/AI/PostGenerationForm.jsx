import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Sparkles, ChevronDown } from 'lucide-react'
import Card from '../UI/Card'
import Button from '../UI/Button'
import { aiApi } from '../../api/mockApi'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants'

const PostGenerationForm = ({ onGenerate, isGenerating }) => {
  const [niches, setNiches] = useState([])
  const [selectedNiche, setSelectedNiche] = useState('')
  const [prompt, setPrompt] = useState('')
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [errors, setErrors] = useState({})

  useEffect(() => {
    const loadNiches = async () => {
      try {
        const nicheData = await aiApi.getNiches()
        setNiches(nicheData)
      } catch (error) {
        console.error('Failed to load niches:', error)
      }
    }
    loadNiches()
  }, [])

  const validateForm = () => {
    const newErrors = {}
    
    if (!selectedNiche) {
      newErrors.niche = 'Please select a niche'
    }
    
    if (!prompt.trim()) {
      newErrors.prompt = 'Please enter a prompt'
    } else if (prompt.trim().length < 10) {
      newErrors.prompt = 'Prompt should be at least 10 characters'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    onGenerate(selectedNiche, prompt.trim())
  }

  const handleNicheSelect = (niche) => {
    setSelectedNiche(niche)
    setIsDropdownOpen(false)
    if (errors.niche) {
      setErrors(prev => ({ ...prev, niche: '' }))
    }
  }

  const handlePromptChange = (e) => {
    setPrompt(e.target.value)
    if (errors.prompt) {
      setErrors(prev => ({ ...prev, prompt: '' }))
    }
  }

  return (
    <Card className="generation-form">
      <div className="form-header">
        <div className="header-icon">
          <Sparkles size={24} />
        </div>
        <div className="header-content">
          <h2>AI Content Generation</h2>
          <p>Create engaging posts with AI assistance</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="form">
        {/* Niche Selection */}
        <div className="form-group">
          <label htmlFor="niche" className="form-label">
            Content Niche
          </label>
          <div className="dropdown-container">
            <button
              type="button"
              className={`dropdown-trigger ${errors.niche ? 'error' : ''}`}
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
              disabled={isGenerating}
            >
              <span className={selectedNiche ? 'selected' : 'placeholder'}>
                {selectedNiche || 'Select a niche...'}
              </span>
              <ChevronDown 
                size={20} 
                className={`dropdown-icon ${isDropdownOpen ? 'rotated' : ''}`}
              />
            </button>
            
            {isDropdownOpen && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="dropdown-menu"
              >
                {niches.map((niche) => (
                  <button
                    key={niche}
                    type="button"
                    className={`dropdown-item ${selectedNiche === niche ? 'selected' : ''}`}
                    onClick={() => handleNicheSelect(niche)}
                  >
                    {niche}
                  </button>
                ))}
              </motion.div>
            )}
          </div>
          {errors.niche && (
            <div className="error-message">{errors.niche}</div>
          )}
        </div>

        {/* Prompt Input */}
        <div className="form-group">
          <label htmlFor="prompt" className="form-label">
            Content Prompt
          </label>
          <textarea
            id="prompt"
            value={prompt}
            onChange={handlePromptChange}
            placeholder="What do you want to talk about? Be specific. e.g., 'Five benefits of using AI in content creation.'"
            className={`form-textarea ${errors.prompt ? 'error' : ''}`}
            rows={4}
            disabled={isGenerating}
          />
          <div className="textarea-footer">
            <span className="character-count">
              {prompt.length} characters
            </span>
            {errors.prompt && (
              <div className="error-message">{errors.prompt}</div>
            )}
          </div>
        </div>

        {/* Submit Button */}
        <Button
          type="submit"
          variant="primary"
          size="lg"
          loading={isGenerating}
          disabled={isGenerating}
          icon={<Sparkles size={20} />}
          className="generate-button"
        >
          {isGenerating ? 'Generating Posts...' : 'Generate Posts'}
        </Button>
      </form>

      <style jsx>{`
        .generation-form {
          max-width: 600px;
          margin: 0 auto;
        }

        .form-header {
          display: flex;
          align-items: center;
          gap: ${SPACING.lg};
          margin-bottom: ${SPACING['2xl']};
          padding-bottom: ${SPACING.lg};
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .header-icon {
          background: ${COLORS.primary}15;
          color: ${COLORS.primary};
          padding: ${SPACING.md};
          border-radius: ${BORDER_RADIUS.lg};
        }

        .header-content h2 {
          font-size: ${TYPOGRAPHY.fontSize['2xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.xs} 0;
        }

        .header-content p {
          color: ${COLORS.textSecondary};
          margin: 0;
        }

        .form {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.xl};
        }

        .form-group {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.sm};
        }

        .form-label {
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          color: ${COLORS.textPrimary};
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .dropdown-container {
          position: relative;
        }

        .dropdown-trigger {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: ${SPACING.md};
          border: 1px solid ${COLORS.gray300};
          border-radius: ${BORDER_RADIUS.lg};
          background: ${COLORS.background};
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: left;
        }

        .dropdown-trigger:hover:not(:disabled) {
          border-color: ${COLORS.gray400};
        }

        .dropdown-trigger:focus {
          outline: none;
          border-color: ${COLORS.primary};
          box-shadow: 0 0 0 3px ${COLORS.primary}20;
        }

        .dropdown-trigger.error {
          border-color: ${COLORS.error};
        }

        .dropdown-trigger:disabled {
          background: ${COLORS.gray100};
          cursor: not-allowed;
          opacity: 0.6;
        }

        .dropdown-trigger .placeholder {
          color: ${COLORS.textMuted};
        }

        .dropdown-trigger .selected {
          color: ${COLORS.textPrimary};
        }

        .dropdown-icon {
          transition: transform 0.2s ease;
          color: ${COLORS.gray500};
        }

        .dropdown-icon.rotated {
          transform: rotate(180deg);
        }

        .dropdown-menu {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: ${COLORS.background};
          border: 1px solid ${COLORS.gray200};
          border-radius: ${BORDER_RADIUS.lg};
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
          z-index: 50;
          max-height: 200px;
          overflow-y: auto;
          margin-top: ${SPACING.xs};
        }

        .dropdown-item {
          width: 100%;
          padding: ${SPACING.md};
          text-align: left;
          border: none;
          background: none;
          cursor: pointer;
          transition: background-color 0.2s ease;
          color: ${COLORS.textPrimary};
        }

        .dropdown-item:hover {
          background: ${COLORS.gray100};
        }

        .dropdown-item.selected {
          background: ${COLORS.primary}10;
          color: ${COLORS.primary};
        }

        .form-textarea {
          width: 100%;
          padding: ${SPACING.md};
          border: 1px solid ${COLORS.gray300};
          border-radius: ${BORDER_RADIUS.lg};
          font-family: inherit;
          font-size: ${TYPOGRAPHY.fontSize.base};
          line-height: ${TYPOGRAPHY.lineHeight.relaxed};
          resize: vertical;
          min-height: 120px;
          transition: all 0.2s ease;
        }

        .form-textarea:focus {
          outline: none;
          border-color: ${COLORS.primary};
          box-shadow: 0 0 0 3px ${COLORS.primary}20;
        }

        .form-textarea.error {
          border-color: ${COLORS.error};
        }

        .form-textarea:disabled {
          background: ${COLORS.gray100};
          cursor: not-allowed;
          opacity: 0.6;
        }

        .textarea-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .character-count {
          font-size: ${TYPOGRAPHY.fontSize.xs};
          color: ${COLORS.textMuted};
        }

        .error-message {
          color: ${COLORS.error};
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .generate-button {
          width: 100%;
          margin-top: ${SPACING.lg};
        }
      `}</style>
    </Card>
  )
}

export default PostGenerationForm
