import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Edit, 
  Trash2, 
  Check, 
  Calendar, 
  Send,
  Image as ImageIcon,
  MoreHorizontal
} from 'lucide-react'
import Card from '../UI/Card'
import Button from '../UI/Button'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS, SOCIAL_LIMITS } from '../../utils/constants'
import { getRemainingCharacters } from '../../utils/helpers'

const GeneratedPostCard = ({ 
  post, 
  onEdit, 
  onDelete, 
  onApprove, 
  onSchedule,
  onPublishNow 
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editedText, setEditedText] = useState(post.text)
  const [showActions, setShowActions] = useState(false)

  const remainingChars = getRemainingCharacters(editedText, SOCIAL_LIMITS.TWITTER.MAX_CHARACTERS)
  const isOverLimit = remainingChars < 0

  const handleSaveEdit = () => {
    if (!isOverLimit && editedText.trim()) {
      onEdit(post.id, { text: editedText.trim() })
      setIsEditing(false)
    }
  }

  const handleCancelEdit = () => {
    setEditedText(post.text)
    setIsEditing(false)
  }

  const getStatusColor = () => {
    switch (post.status) {
      case 'approved':
        return COLORS.success
      case 'pending':
        return COLORS.warning
      default:
        return COLORS.gray500
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="generated-post-card">
        {/* Header */}
        <div className="post-header">
          <div className="status-section">
            <div 
              className="status-indicator"
              style={{ backgroundColor: getStatusColor() }}
            />
            <span className="status-text">
              {post.status === 'approved' ? 'Approved' : 'Pending Review'}
            </span>
          </div>
          
          <div className="post-actions">
            <Button
              variant="ghost"
              size="sm"
              icon={<Edit size={16} />}
              onClick={() => setIsEditing(true)}
              disabled={isEditing}
            />
            <Button
              variant="ghost"
              size="sm"
              icon={<Trash2 size={16} />}
              onClick={() => onDelete(post.id)}
            />
            <div className="more-actions">
              <Button
                variant="ghost"
                size="sm"
                icon={<MoreHorizontal size={16} />}
                onClick={() => setShowActions(!showActions)}
              />
              {showActions && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="actions-dropdown"
                >
                  <button 
                    className="action-item"
                    onClick={() => onSchedule(post.id)}
                  >
                    <Calendar size={16} />
                    Schedule
                  </button>
                  <button 
                    className="action-item"
                    onClick={() => onPublishNow(post.id)}
                  >
                    <Send size={16} />
                    Publish Now
                  </button>
                </motion.div>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="post-content">
          {isEditing ? (
            <div className="edit-section">
              <textarea
                value={editedText}
                onChange={(e) => setEditedText(e.target.value)}
                className={`edit-textarea ${isOverLimit ? 'over-limit' : ''}`}
                rows={4}
                autoFocus
              />
              <div className="edit-footer">
                <div className="character-counter">
                  <span className={isOverLimit ? 'over-limit' : ''}>
                    {remainingChars} characters remaining
                  </span>
                </div>
                <div className="edit-actions">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCancelEdit}
                  >
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleSaveEdit}
                    disabled={isOverLimit || !editedText.trim()}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div className="post-text">
              {post.text}
            </div>
          )}

          {/* Media Preview */}
          {post.media && (
            <div className="media-section">
              <div className="media-preview">
                {post.media.type === 'image' ? (
                  <img 
                    src={post.media.url} 
                    alt={post.media.alt}
                    className="media-image"
                  />
                ) : (
                  <div className="media-placeholder">
                    <ImageIcon size={24} />
                    <span>Media attached</span>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="post-footer">
          <div className="niche-tag">
            {post.niche}
          </div>
          
          {post.status === 'pending' && !isEditing && (
            <Button
              variant="primary"
              size="sm"
              icon={<Check size={16} />}
              onClick={() => onApprove(post.id)}
            >
              Approve
            </Button>
          )}
        </div>

        <style jsx>{`
          .generated-post-card {
            border-left: 4px solid ${getStatusColor()};
            position: relative;
          }

          .post-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: ${SPACING.lg};
          }

          .status-section {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
          }

          .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }

          .status-text {
            font-size: ${TYPOGRAPHY.fontSize.sm};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
            color: ${COLORS.textSecondary};
          }

          .post-actions {
            display: flex;
            align-items: center;
            gap: ${SPACING.xs};
          }

          .more-actions {
            position: relative;
          }

          .actions-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: ${COLORS.background};
            border: 1px solid ${COLORS.gray200};
            border-radius: ${BORDER_RADIUS.lg};
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 10;
            min-width: 140px;
            margin-top: ${SPACING.xs};
          }

          .action-item {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
            width: 100%;
            padding: ${SPACING.md};
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: ${COLORS.textPrimary};
            font-size: ${TYPOGRAPHY.fontSize.sm};
          }

          .action-item:hover {
            background: ${COLORS.gray100};
          }

          .post-content {
            margin-bottom: ${SPACING.lg};
          }

          .post-text {
            color: ${COLORS.textPrimary};
            line-height: ${TYPOGRAPHY.lineHeight.relaxed};
            white-space: pre-wrap;
            margin-bottom: ${SPACING.md};
          }

          .edit-section {
            margin-bottom: ${SPACING.md};
          }

          .edit-textarea {
            width: 100%;
            padding: ${SPACING.md};
            border: 1px solid ${COLORS.gray300};
            border-radius: ${BORDER_RADIUS.lg};
            font-family: inherit;
            font-size: ${TYPOGRAPHY.fontSize.base};
            line-height: ${TYPOGRAPHY.lineHeight.relaxed};
            resize: vertical;
            min-height: 100px;
          }

          .edit-textarea:focus {
            outline: none;
            border-color: ${COLORS.primary};
            box-shadow: 0 0 0 3px ${COLORS.primary}20;
          }

          .edit-textarea.over-limit {
            border-color: ${COLORS.error};
          }

          .edit-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: ${SPACING.sm};
          }

          .character-counter {
            font-size: ${TYPOGRAPHY.fontSize.xs};
            color: ${COLORS.textMuted};
          }

          .character-counter .over-limit {
            color: ${COLORS.error};
          }

          .edit-actions {
            display: flex;
            gap: ${SPACING.sm};
          }

          .media-section {
            margin-top: ${SPACING.md};
          }

          .media-preview {
            border-radius: ${BORDER_RADIUS.lg};
            overflow: hidden;
            border: 1px solid ${COLORS.gray200};
          }

          .media-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
          }

          .media-placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 120px;
            background: ${COLORS.gray100};
            color: ${COLORS.textMuted};
            gap: ${SPACING.sm};
          }

          .post-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: ${SPACING.md};
            border-top: 1px solid ${COLORS.gray200};
          }

          .niche-tag {
            background: ${COLORS.primary}15;
            color: ${COLORS.primary};
            padding: ${SPACING.xs} ${SPACING.sm};
            border-radius: ${BORDER_RADIUS.md};
            font-size: ${TYPOGRAPHY.fontSize.xs};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
          }
        `}</style>
      </Card>
    </motion.div>
  )
}

export default GeneratedPostCard
