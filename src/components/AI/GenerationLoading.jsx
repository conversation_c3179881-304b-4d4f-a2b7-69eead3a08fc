import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from 'lucide-react'
import Card from '../UI/Card'
import { COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants'

const GenerationLoading = () => {
  const loadingSteps = [
    { icon: Brain, text: "Analyzing your prompt...", delay: 0 },
    { icon: Spark<PERSON>, text: "Generating creative content...", delay: 1 },
    { icon: Zap, text: "Optimizing for engagement...", delay: 2 }
  ]

  return (
    <Card className="generation-loading">
      <div className="loading-content">
        {/* Main spinner */}
        <div className="main-spinner-container">
          <motion.div
            className="main-spinner"
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Sparkles size={32} />
          </motion.div>
          
          <motion.div
            className="pulse-ring"
            animate={{ scale: [1, 1.2, 1], opacity: [0.7, 0.3, 0.7] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>

        {/* Loading message */}
        <div className="loading-message">
          <h3>The AI is crafting your posts...</h3>
          <p>This usually takes a few seconds</p>
        </div>

        {/* Loading steps */}
        <div className="loading-steps">
          {loadingSteps.map((step, index) => (
            <motion.div
              key={index}
              className="loading-step"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: step.delay, duration: 0.5 }}
            >
              <div className="step-icon">
                <step.icon size={16} />
              </div>
              <span className="step-text">{step.text}</span>
              <motion.div
                className="step-spinner"
                animate={{ rotate: 360 }}
                transition={{ 
                  duration: 1, 
                  repeat: Infinity, 
                  ease: "linear",
                  delay: step.delay 
                }}
              >
                <div className="mini-spinner" />
              </motion.div>
            </motion.div>
          ))}
        </div>

        {/* Progress bar */}
        <div className="progress-container">
          <div className="progress-bar">
            <motion.div
              className="progress-fill"
              initial={{ width: "0%" }}
              animate={{ width: "100%" }}
              transition={{ duration: 3, ease: "easeInOut" }}
            />
          </div>
          <div className="progress-text">
            <motion.span
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              Generating amazing content for you...
            </motion.span>
          </div>
        </div>
      </div>

      <style jsx>{`
        .generation-loading {
          text-align: center;
          padding: ${SPACING['3xl']};
          max-width: 500px;
          margin: 0 auto;
        }

        .loading-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: ${SPACING['2xl']};
        }

        .main-spinner-container {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .main-spinner {
          color: ${COLORS.primary};
          z-index: 2;
          position: relative;
        }

        .pulse-ring {
          position: absolute;
          width: 80px;
          height: 80px;
          border: 2px solid ${COLORS.primary};
          border-radius: 50%;
          z-index: 1;
        }

        .loading-message h3 {
          font-size: ${TYPOGRAPHY.fontSize.xl};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.sm} 0;
        }

        .loading-message p {
          color: ${COLORS.textSecondary};
          margin: 0;
        }

        .loading-steps {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.lg};
          width: 100%;
          max-width: 300px;
        }

        .loading-step {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
          padding: ${SPACING.md};
          background: ${COLORS.gray50};
          border-radius: ${SPACING.md};
          border: 1px solid ${COLORS.gray200};
        }

        .step-icon {
          color: ${COLORS.primary};
          flex-shrink: 0;
        }

        .step-text {
          flex: 1;
          text-align: left;
          font-size: ${TYPOGRAPHY.fontSize.sm};
          color: ${COLORS.textPrimary};
        }

        .step-spinner {
          flex-shrink: 0;
        }

        .mini-spinner {
          width: 12px;
          height: 12px;
          border: 2px solid ${COLORS.gray300};
          border-top: 2px solid ${COLORS.primary};
          border-radius: 50%;
        }

        .progress-container {
          width: 100%;
          max-width: 300px;
        }

        .progress-bar {
          width: 100%;
          height: 4px;
          background: ${COLORS.gray200};
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: ${SPACING.md};
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, ${COLORS.primary}, ${COLORS.secondary});
          border-radius: 2px;
        }

        .progress-text {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          color: ${COLORS.textSecondary};
        }

        /* Floating animation for the main container */
        .generation-loading {
          animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-5px);
          }
        }

        /* Shimmer effect */
        .loading-step::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.4),
            transparent
          );
          animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
          0% {
            left: -100%;
          }
          100% {
            left: 100%;
          }
        }
      `}</style>
    </Card>
  )
}

export default GenerationLoading
