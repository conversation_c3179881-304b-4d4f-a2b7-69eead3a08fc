import { motion } from 'framer-motion'
import Card from '../UI/Card'
import { COLORS, SPACING, TYPOGRAPHY } from '../../utils/constants'
import { formatNumber, formatPercentage } from '../../utils/helpers'

const AnalyticsCard = ({ 
  title, 
  value, 
  change, 
  changeType = 'positive', 
  icon, 
  subtitle,
  trend 
}) => {
  const changeColor = changeType === 'positive' ? COLORS.success : 
                     changeType === 'negative' ? COLORS.error : 
                     COLORS.textSecondary

  return (
    <Card hover className="analytics-card">
      <div className="card-header">
        <div className="card-title-section">
          <h3 className="card-title">{title}</h3>
          {subtitle && <p className="card-subtitle">{subtitle}</p>}
        </div>
        {icon && (
          <div className="card-icon">
            {icon}
          </div>
        )}
      </div>

      <div className="card-content">
        <div className="main-value">
          {typeof value === 'number' ? formatNumber(value) : value}
        </div>
        
        {change !== undefined && (
          <div className="change-indicator">
            <span className="change-value" style={{ color: changeColor }}>
              {changeType === 'positive' && change > 0 ? '+' : ''}
              {typeof change === 'number' ? formatPercentage(change) : change}
            </span>
            <span className="change-label">vs last period</span>
          </div>
        )}

        {trend && (
          <div className="trend-section">
            <div className="trend-chart">
              {trend.map((point, index) => (
                <div
                  key={index}
                  className="trend-bar"
                  style={{
                    height: `${(point / Math.max(...trend)) * 100}%`,
                    backgroundColor: changeType === 'positive' ? COLORS.success : 
                                   changeType === 'negative' ? COLORS.error : 
                                   COLORS.primary
                  }}
                />
              ))}
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .analytics-card {
          min-height: 140px;
        }

        .card-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: ${SPACING.lg};
        }

        .card-title {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          font-weight: ${TYPOGRAPHY.fontWeight.medium};
          color: ${COLORS.textSecondary};
          margin: 0;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .card-subtitle {
          font-size: ${TYPOGRAPHY.fontSize.xs};
          color: ${COLORS.textMuted};
          margin: ${SPACING.xs} 0 0 0;
        }

        .card-icon {
          color: ${COLORS.primary};
          opacity: 0.7;
        }

        .main-value {
          font-size: ${TYPOGRAPHY.fontSize['3xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin-bottom: ${SPACING.sm};
          line-height: 1;
        }

        .change-indicator {
          display: flex;
          align-items: center;
          gap: ${SPACING.sm};
          margin-bottom: ${SPACING.md};
        }

        .change-value {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
        }

        .change-label {
          font-size: ${TYPOGRAPHY.fontSize.xs};
          color: ${COLORS.textMuted};
        }

        .trend-section {
          margin-top: auto;
        }

        .trend-chart {
          display: flex;
          align-items: end;
          gap: 2px;
          height: 32px;
        }

        .trend-bar {
          flex: 1;
          min-height: 4px;
          border-radius: 1px;
          transition: all 0.3s ease;
        }

        .trend-bar:hover {
          opacity: 0.8;
        }
      `}</style>
    </Card>
  )
}

export default AnalyticsCard
