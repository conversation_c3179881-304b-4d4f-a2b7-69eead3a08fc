import { motion } from 'framer-motion'
import { 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Copy,
  MoreVertical,
  Calendar,
  Activity
} from 'lucide-react'
import Card from '../UI/Card'
import Button from '../UI/Button'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants'
import { formatRelativeTime } from '../../utils/helpers'

const TemplateCard = ({ 
  template, 
  onEdit, 
  onDelete, 
  onToggleActive, 
  onDuplicate,
  onTest 
}) => {
  const getStatusColor = () => {
    return template.isActive ? COLORS.success : COLORS.gray400
  }

  const getStatusText = () => {
    return template.isActive ? 'Active' : 'Inactive'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="template-card" hover>
        {/* Header */}
        <div className="card-header">
          <div className="template-info">
            <h3 className="template-name">{template.name}</h3>
            <p className="template-description">{template.description}</p>
          </div>
          
          <div className="template-status">
            <div 
              className="status-indicator"
              style={{ backgroundColor: getStatusColor() }}
            />
            <span className="status-text">{getStatusText()}</span>
          </div>
        </div>

        {/* Metadata */}
        <div className="template-metadata">
          <div className="metadata-item">
            <Calendar size={14} />
            <span>Created {formatRelativeTime(template.createdAt)}</span>
          </div>
          
          {template.lastRun && (
            <div className="metadata-item">
              <Activity size={14} />
              <span>Last run {formatRelativeTime(template.lastRun)}</span>
            </div>
          )}
        </div>

        {/* Code Preview */}
        <div className="code-preview">
          <pre className="code-snippet">
            {template.code.split('\n').slice(0, 3).join('\n')}
            {template.code.split('\n').length > 3 && '\n...'}
          </pre>
        </div>

        {/* Actions */}
        <div className="card-actions">
          <div className="primary-actions">
            <Button
              variant="outline"
              size="sm"
              icon={<Edit size={14} />}
              onClick={() => onEdit(template.id)}
            >
              Edit
            </Button>
            
            <Button
              variant="outline"
              size="sm"
              icon={<Play size={14} />}
              onClick={() => onTest(template.id)}
            >
              Test
            </Button>
          </div>

          <div className="secondary-actions">
            <Button
              variant={template.isActive ? 'outline' : 'primary'}
              size="sm"
              icon={template.isActive ? <Pause size={14} /> : <Play size={14} />}
              onClick={() => onToggleActive(template.id)}
            >
              {template.isActive ? 'Deactivate' : 'Activate'}
            </Button>
            
            <div className="dropdown-actions">
              <Button
                variant="ghost"
                size="sm"
                icon={<MoreVertical size={14} />}
                onClick={() => console.log('More actions')}
              />
              {/* Dropdown menu would go here */}
            </div>
          </div>
        </div>

        <style jsx>{`
          .template-card {
            border-left: 4px solid ${getStatusColor()};
          }

          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: ${SPACING.md};
          }

          .template-info {
            flex: 1;
          }

          .template-name {
            font-size: ${TYPOGRAPHY.fontSize.lg};
            font-weight: ${TYPOGRAPHY.fontWeight.semibold};
            color: ${COLORS.textPrimary};
            margin: 0 0 ${SPACING.xs} 0;
          }

          .template-description {
            color: ${COLORS.textSecondary};
            margin: 0;
            font-size: ${TYPOGRAPHY.fontSize.sm};
            line-height: ${TYPOGRAPHY.lineHeight.relaxed};
          }

          .template-status {
            display: flex;
            align-items: center;
            gap: ${SPACING.sm};
            flex-shrink: 0;
          }

          .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
          }

          .status-text {
            font-size: ${TYPOGRAPHY.fontSize.sm};
            font-weight: ${TYPOGRAPHY.fontWeight.medium};
            color: ${COLORS.textSecondary};
          }

          .template-metadata {
            display: flex;
            flex-wrap: wrap;
            gap: ${SPACING.lg};
            margin-bottom: ${SPACING.md};
          }

          .metadata-item {
            display: flex;
            align-items: center;
            gap: ${SPACING.xs};
            font-size: ${TYPOGRAPHY.fontSize.xs};
            color: ${COLORS.textMuted};
          }

          .code-preview {
            background: ${COLORS.gray900};
            border-radius: ${BORDER_RADIUS.md};
            padding: ${SPACING.md};
            margin-bottom: ${SPACING.lg};
            overflow: hidden;
          }

          .code-snippet {
            color: ${COLORS.gray100};
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;
            margin: 0;
            white-space: pre-wrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .card-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: ${SPACING.md};
            padding-top: ${SPACING.md};
            border-top: 1px solid ${COLORS.gray200};
          }

          .primary-actions,
          .secondary-actions {
            display: flex;
            gap: ${SPACING.sm};
            align-items: center;
          }

          .dropdown-actions {
            position: relative;
          }
        `}</style>
      </Card>
    </motion.div>
  )
}

const TemplateList = ({ 
  templates, 
  onEdit, 
  onDelete, 
  onToggleActive, 
  onDuplicate,
  onTest,
  onCreateNew 
}) => {
  if (templates.length === 0) {
    return (
      <Card className="empty-state">
        <div className="empty-content">
          <div className="empty-icon">📝</div>
          <h3>No templates yet</h3>
          <p>Create your first template to automate content generation</p>
          <Button
            variant="primary"
            onClick={onCreateNew}
          >
            Create Template
          </Button>
        </div>

        <style jsx>{`
          .empty-state {
            text-align: center;
            padding: ${SPACING['4xl']};
          }

          .empty-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: ${SPACING.lg};
          }

          .empty-icon {
            font-size: 4rem;
            opacity: 0.5;
          }

          .empty-content h3 {
            font-size: ${TYPOGRAPHY.fontSize.xl};
            font-weight: ${TYPOGRAPHY.fontWeight.semibold};
            color: ${COLORS.textPrimary};
            margin: 0;
          }

          .empty-content p {
            color: ${COLORS.textSecondary};
            margin: 0;
            max-width: 300px;
          }
        `}</style>
      </Card>
    )
  }

  return (
    <div className="template-list">
      <div className="templates-grid">
        {templates.map((template) => (
          <TemplateCard
            key={template.id}
            template={template}
            onEdit={onEdit}
            onDelete={onDelete}
            onToggleActive={onToggleActive}
            onDuplicate={onDuplicate}
            onTest={onTest}
          />
        ))}
      </div>

      <style jsx>{`
        .template-list {
          width: 100%;
        }

        .templates-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
          gap: ${SPACING.xl};
        }

        @media (max-width: 768px) {
          .templates-grid {
            grid-template-columns: 1fr;
            gap: ${SPACING.lg};
          }
        }
      `}</style>
    </div>
  )
}

export default TemplateList
