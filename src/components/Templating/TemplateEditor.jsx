import { useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { 
  Play, 
  Save, 
  Upload, 
  Download,
  Settings,
  Eye,
  Code
} from 'lucide-react'
import Card from '../UI/Card'
import Button from '../UI/Button'
import { COLORS, SPACING, TYPOGRAPHY, BORDER_RADIUS } from '../../utils/constants'

const TemplateEditor = ({ 
  template, 
  onSave, 
  onTest, 
  onDeploy,
  testResult,
  isLoading 
}) => {
  const [code, setCode] = useState(template?.code || '')
  const [testData, setTestData] = useState('{\n  "symbol": "BTC",\n  "price": 65000,\n  "change": 1250,\n  "changePercent": 2.5\n}')
  const [activeTab, setActiveTab] = useState('editor')
  const editorRef = useRef(null)

  const handleSave = () => {
    onSave({
      ...template,
      code: code
    })
  }

  const handleTest = () => {
    try {
      const parsedTestData = JSON.parse(testData)
      onTest(template?.id, parsedTestData)
    } catch (error) {
      console.error('Invalid test data JSON:', error)
    }
  }

  const handleDeploy = () => {
    onDeploy(template?.id)
  }

  const defaultCode = `function generatePost(data) {
  // Extract data from the input
  const { symbol, price, change, changePercent } = data;
  
  // Determine emoji and direction based on change
  const emoji = changePercent > 0 ? '🚀' : '📉';
  const direction = changePercent > 0 ? 'up' : 'down';
  
  // Generate the post content
  return {
    text: \`\${emoji} \${symbol} is \${direction} \${Math.abs(changePercent).toFixed(2)}% to $\${price.toLocaleString()}!
    
#\${symbol} #Crypto #PriceAlert\`,
    media: null
  };
}`

  return (
    <div className="template-editor">
      {/* Header */}
      <div className="editor-header">
        <div className="header-info">
          <h2>{template?.name || 'New Template'}</h2>
          <p>{template?.description || 'Create a new template'}</p>
        </div>
        
        <div className="header-actions">
          <Button
            variant="outline"
            size="sm"
            icon={<Download size={16} />}
            onClick={() => console.log('Export template')}
          >
            Export
          </Button>
          <Button
            variant="outline"
            size="sm"
            icon={<Upload size={16} />}
            onClick={() => console.log('Import template')}
          >
            Import
          </Button>
          <Button
            variant="primary"
            size="sm"
            icon={<Save size={16} />}
            onClick={handleSave}
          >
            Save
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button
          className={`tab ${activeTab === 'editor' ? 'active' : ''}`}
          onClick={() => setActiveTab('editor')}
        >
          <Code size={16} />
          Editor
        </button>
        <button
          className={`tab ${activeTab === 'test' ? 'active' : ''}`}
          onClick={() => setActiveTab('test')}
        >
          <Play size={16} />
          Test
        </button>
        <button
          className={`tab ${activeTab === 'preview' ? 'active' : ''}`}
          onClick={() => setActiveTab('preview')}
        >
          <Eye size={16} />
          Preview
        </button>
      </div>

      {/* Content */}
      <div className="editor-content">
        {activeTab === 'editor' && (
          <Card className="editor-panel">
            <div className="code-editor">
              <textarea
                ref={editorRef}
                value={code || defaultCode}
                onChange={(e) => setCode(e.target.value)}
                className="code-textarea"
                placeholder="Write your template function here..."
                spellCheck={false}
              />
            </div>
            
            <div className="editor-footer">
              <div className="editor-info">
                <span className="line-count">
                  {(code || defaultCode).split('\n').length} lines
                </span>
                <span className="char-count">
                  {(code || defaultCode).length} characters
                </span>
              </div>
              
              <div className="editor-actions">
                <Button
                  variant="outline"
                  size="sm"
                  icon={<Settings size={16} />}
                  onClick={() => console.log('Editor settings')}
                >
                  Settings
                </Button>
              </div>
            </div>
          </Card>
        )}

        {activeTab === 'test' && (
          <div className="test-panel">
            <Card className="test-input">
              <h3>Test Data</h3>
              <textarea
                value={testData}
                onChange={(e) => setTestData(e.target.value)}
                className="test-data-input"
                rows={8}
                placeholder="Enter test data as JSON..."
              />
              <Button
                variant="primary"
                icon={<Play size={16} />}
                onClick={handleTest}
                loading={isLoading}
                disabled={isLoading}
              >
                Run Test
              </Button>
            </Card>

            <Card className="test-output">
              <h3>Output</h3>
              {testResult ? (
                <div className="test-result">
                  {testResult.success ? (
                    <div className="success-result">
                      <h4>Generated Post:</h4>
                      <div className="post-preview">
                        <p>{testResult.result.text}</p>
                        {testResult.result.media && (
                          <div className="media-indicator">
                            📎 Media attached
                          </div>
                        )}
                      </div>
                      
                      {testResult.logs && (
                        <div className="logs">
                          <h4>Logs:</h4>
                          {testResult.logs.map((log, index) => (
                            <div key={index} className="log-entry">
                              {log}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="error-result">
                      <h4>Error:</h4>
                      <p>{testResult.error}</p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="no-result">
                  <p>Run a test to see the output</p>
                </div>
              )}
            </Card>
          </div>
        )}

        {activeTab === 'preview' && (
          <Card className="preview-panel">
            <h3>Template Preview</h3>
            {testResult?.success ? (
              <div className="post-preview-card">
                <div className="preview-header">
                  <div className="avatar">👤</div>
                  <div className="user-info">
                    <div className="username">Your Account</div>
                    <div className="timestamp">Just now</div>
                  </div>
                </div>
                <div className="preview-content">
                  <p>{testResult.result.text}</p>
                  {testResult.result.media && (
                    <div className="preview-media">
                      <div className="media-placeholder">
                        📷 Media would appear here
                      </div>
                    </div>
                  )}
                </div>
                <div className="preview-actions">
                  <span>💬 Reply</span>
                  <span>🔄 Retweet</span>
                  <span>❤️ Like</span>
                </div>
              </div>
            ) : (
              <div className="no-preview">
                <p>Run a test to see the preview</p>
              </div>
            )}
          </Card>
        )}
      </div>

      {/* Deploy Section */}
      {template?.id && (
        <Card className="deploy-section">
          <div className="deploy-content">
            <div className="deploy-info">
              <h3>Deploy Template</h3>
              <p>Make this template active and ready for automation</p>
            </div>
            <Button
              variant="primary"
              icon={<Upload size={16} />}
              onClick={handleDeploy}
              disabled={!testResult?.success}
            >
              Deploy Template
            </Button>
          </div>
        </Card>
      )}

      <style jsx>{`
        .template-editor {
          display: flex;
          flex-direction: column;
          gap: ${SPACING.lg};
          height: calc(100vh - 200px);
        }

        .editor-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          gap: ${SPACING.lg};
        }

        .header-info h2 {
          font-size: ${TYPOGRAPHY.fontSize['2xl']};
          font-weight: ${TYPOGRAPHY.fontWeight.bold};
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.xs} 0;
        }

        .header-info p {
          color: ${COLORS.textSecondary};
          margin: 0;
        }

        .header-actions {
          display: flex;
          gap: ${SPACING.sm};
        }

        .tab-navigation {
          display: flex;
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .tab {
          display: flex;
          align-items: center;
          gap: ${SPACING.sm};
          padding: ${SPACING.md} ${SPACING.lg};
          border: none;
          background: none;
          color: ${COLORS.textSecondary};
          cursor: pointer;
          border-bottom: 2px solid transparent;
          transition: all 0.2s ease;
        }

        .tab:hover {
          color: ${COLORS.textPrimary};
          background: ${COLORS.gray50};
        }

        .tab.active {
          color: ${COLORS.primary};
          border-bottom-color: ${COLORS.primary};
        }

        .editor-content {
          flex: 1;
          min-height: 0;
        }

        .editor-panel {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .code-editor {
          flex: 1;
          min-height: 0;
        }

        .code-textarea {
          width: 100%;
          height: 100%;
          min-height: 400px;
          padding: ${SPACING.lg};
          border: none;
          background: ${COLORS.gray900};
          color: ${COLORS.gray100};
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          line-height: 1.5;
          resize: none;
          outline: none;
          border-radius: ${BORDER_RADIUS.lg};
        }

        .editor-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: ${SPACING.md};
          border-top: 1px solid ${COLORS.gray200};
          background: ${COLORS.gray50};
        }

        .editor-info {
          display: flex;
          gap: ${SPACING.lg};
          font-size: ${TYPOGRAPHY.fontSize.xs};
          color: ${COLORS.textMuted};
        }

        .test-panel {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: ${SPACING.lg};
          height: 100%;
        }

        .test-input h3,
        .test-output h3 {
          margin: 0 0 ${SPACING.md} 0;
          color: ${COLORS.textPrimary};
        }

        .test-data-input {
          width: 100%;
          padding: ${SPACING.md};
          border: 1px solid ${COLORS.gray300};
          border-radius: ${BORDER_RADIUS.lg};
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 14px;
          margin-bottom: ${SPACING.md};
          resize: vertical;
        }

        .test-result {
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        .post-preview {
          background: ${COLORS.gray50};
          padding: ${SPACING.md};
          border-radius: ${BORDER_RADIUS.lg};
          margin: ${SPACING.md} 0;
          border-left: 4px solid ${COLORS.primary};
        }

        .post-preview p {
          margin: 0;
          white-space: pre-wrap;
          line-height: 1.5;
        }

        .media-indicator {
          margin-top: ${SPACING.sm};
          color: ${COLORS.textMuted};
          font-size: ${TYPOGRAPHY.fontSize.xs};
        }

        .logs {
          margin-top: ${SPACING.md};
        }

        .log-entry {
          background: ${COLORS.gray100};
          padding: ${SPACING.xs} ${SPACING.sm};
          border-radius: ${BORDER_RADIUS.sm};
          font-family: monospace;
          font-size: ${TYPOGRAPHY.fontSize.xs};
          margin-bottom: ${SPACING.xs};
        }

        .error-result {
          color: ${COLORS.error};
        }

        .no-result,
        .no-preview {
          text-align: center;
          color: ${COLORS.textMuted};
          padding: ${SPACING['2xl']};
        }

        .preview-panel h3 {
          margin: 0 0 ${SPACING.lg} 0;
        }

        .post-preview-card {
          border: 1px solid ${COLORS.gray200};
          border-radius: ${BORDER_RADIUS.lg};
          padding: ${SPACING.lg};
          background: ${COLORS.background};
        }

        .preview-header {
          display: flex;
          align-items: center;
          gap: ${SPACING.md};
          margin-bottom: ${SPACING.md};
        }

        .avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: ${COLORS.gray200};
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .username {
          font-weight: ${TYPOGRAPHY.fontWeight.semibold};
          color: ${COLORS.textPrimary};
        }

        .timestamp {
          font-size: ${TYPOGRAPHY.fontSize.sm};
          color: ${COLORS.textMuted};
        }

        .preview-content {
          margin-bottom: ${SPACING.md};
        }

        .preview-content p {
          margin: 0;
          white-space: pre-wrap;
          line-height: 1.5;
        }

        .preview-media {
          margin-top: ${SPACING.md};
        }

        .media-placeholder {
          background: ${COLORS.gray100};
          padding: ${SPACING.lg};
          border-radius: ${BORDER_RADIUS.lg};
          text-align: center;
          color: ${COLORS.textMuted};
        }

        .preview-actions {
          display: flex;
          gap: ${SPACING.lg};
          padding-top: ${SPACING.md};
          border-top: 1px solid ${COLORS.gray200};
          font-size: ${TYPOGRAPHY.fontSize.sm};
          color: ${COLORS.textMuted};
        }

        .preview-actions span {
          cursor: pointer;
          transition: color 0.2s ease;
        }

        .preview-actions span:hover {
          color: ${COLORS.textPrimary};
        }

        .deploy-section {
          margin-top: auto;
        }

        .deploy-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .deploy-info h3 {
          margin: 0 0 ${SPACING.xs} 0;
          color: ${COLORS.textPrimary};
        }

        .deploy-info p {
          margin: 0;
          color: ${COLORS.textSecondary};
          font-size: ${TYPOGRAPHY.fontSize.sm};
        }

        @media (max-width: 1024px) {
          .test-panel {
            grid-template-columns: 1fr;
          }
        }

        @media (max-width: 768px) {
          .header-actions {
            flex-wrap: wrap;
          }

          .tab-navigation {
            overflow-x: auto;
          }
        }
      `}</style>
    </div>
  )
}

export default TemplateEditor
