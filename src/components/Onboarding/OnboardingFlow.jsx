import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  <PERSON>rkles, 
  Zap, 
  Target, 
  Users, 
  TrendingUp, 
  Calendar,
  Twitter,
  Mail,
  Lock,
  Eye,
  EyeOff,
  ArrowRight,
  Check,
  Star
} from 'lucide-react'
import { setLocalStorage } from '../../utils/helpers'
import { STORAGE_KEYS, COLORS, SPACING, BORDER_RADIUS } from '../../utils/constants'
import { authApi } from '../../api/mockApi'

const OnboardingFlow = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState({})
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    name: '',
    goals: []
  })

  const steps = [
    {
      id: 'welcome',
      title: 'Transform Your Twitter Presence',
      subtitle: 'Join 10,000+ creators using AI to grow their audience'
    },
    {
      id: 'goals',
      title: 'What are your goals?',
      subtitle: 'Help us personalize your experience'
    },
    {
      id: 'signup',
      title: 'Create Your Account',
      subtitle: 'Start your content automation journey'
    }
  ]

  const goalOptions = [
    {
      id: 'grow-audience',
      title: 'Grow My Audience',
      description: 'Increase followers and engagement',
      icon: Users,
      color: COLORS.primary
    },
    {
      id: 'save-time',
      title: 'Save Time',
      description: 'Automate content creation and scheduling',
      icon: Zap,
      color: COLORS.secondary
    },
    {
      id: 'improve-content',
      title: 'Improve Content Quality',
      description: 'Create more engaging posts with AI',
      icon: Target,
      color: COLORS.accent
    },
    {
      id: 'track-performance',
      title: 'Track Performance',
      description: 'Monitor analytics and optimize strategy',
      icon: TrendingUp,
      color: COLORS.warning
    }
  ]

  const features = [
    {
      icon: Sparkles,
      title: 'AI Content Generation',
      description: 'Create engaging posts with advanced AI'
    },
    {
      icon: Calendar,
      title: 'Smart Scheduling',
      description: 'Post at optimal times for maximum reach'
    },
    {
      icon: TrendingUp,
      title: 'Analytics Dashboard',
      description: 'Track performance and grow your audience'
    }
  ]

  const validateForm = () => {
    const newErrors = {}
    
    if (steps[currentStep].id === 'goals') {
      if (formData.goals.length === 0) {
        newErrors.goals = 'Please select at least one goal'
      }
    }
    
    if (steps[currentStep].id === 'signup') {
      if (!formData.name.trim()) {
        newErrors.name = 'Name is required'
      }
      
      if (!formData.email) {
        newErrors.email = 'Email is required'
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Please enter a valid email address'
      }
      
      if (!formData.password) {
        newErrors.password = 'Password is required'
      } else if (formData.password.length < 8) {
        newErrors.password = 'Password must be at least 8 characters'
      } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
        newErrors.password = 'Password must contain uppercase, lowercase, and number'
      }
      
      if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match'
      }
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleGoalToggle = (goalId) => {
    setFormData(prev => ({
      ...prev,
      goals: prev.goals.includes(goalId)
        ? prev.goals.filter(id => id !== goalId)
        : [...prev.goals, goalId]
    }))
    
    if (errors.goals) {
      setErrors(prev => ({ ...prev, goals: '' }))
    }
  }

  const handleNext = async () => {
    if (steps[currentStep].id !== 'welcome' && !validateForm()) {
      return
    }

    setIsLoading(true)
    setErrors({})

    try {
      if (steps[currentStep].id === 'signup') {
        await authApi.signUp(formData.email, formData.password)
      }

      if (currentStep < steps.length - 1) {
        setCurrentStep(currentStep + 1)
      } else {
        setLocalStorage(STORAGE_KEYS.ONBOARDING_COMPLETED, true)
        setLocalStorage(STORAGE_KEYS.USER_DATA, {
          name: formData.name,
          email: formData.email,
          goals: formData.goals,
          isConnected: false
        })
        onComplete()
      }
    } catch (error) {
      setErrors({ general: 'Something went wrong. Please try again.' })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
      setErrors({})
    }
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  const renderWelcomeStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -30 }}
      transition={{ duration: 0.6 }}
      className="step-content welcome-step"
    >
      <div className="hero-section">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
          className="hero-icon"
        >
          <Sparkles size={32} />
        </motion.div>
        
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="hero-stats"
        >
          <div className="stat">
            <Star className="stat-icon" />
            <span>10,000+ Creators</span>
          </div>
          <div className="stat">
            <TrendingUp className="stat-icon" />
            <span>2M+ Posts Generated</span>
          </div>
        </motion.div>
      </div>

      <div className="features-grid">
        {features.map((feature, index) => (
          <motion.div
            key={feature.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 + index * 0.1 }}
            className="feature-card"
          >
            <feature.icon className="feature-icon" />
            <h4>{feature.title}</h4>
            <p>{feature.description}</p>
          </motion.div>
        ))}
      </div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.2 }}
        className="cta-section"
      >
        <button className="primary-button large" onClick={handleNext}>
          Start Your Journey
          <ArrowRight size={20} />
        </button>
        <p className="cta-note">Free to start • No credit card required</p>
      </motion.div>
    </motion.div>
  )

  const renderGoalsStep = () => (
    <motion.div
      initial={{ opacity: 0, x: 30 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -30 }}
      transition={{ duration: 0.5 }}
      className="step-content goals-step"
    >
      <div className="goals-grid">
        {goalOptions.map((goal, index) => {
          const Icon = goal.icon
          const isSelected = formData.goals.includes(goal.id)
          
          return (
            <motion.div
              key={goal.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className={`goal-card ${isSelected ? 'selected' : ''}`}
              onClick={() => handleGoalToggle(goal.id)}
            >
              <div className="goal-icon" style={{ color: goal.color }}>
                <Icon size={24} />
              </div>
              <h4>{goal.title}</h4>
              <p>{goal.description}</p>
              <div className="goal-check">
                {isSelected && <Check size={14} />}
              </div>
            </motion.div>
          )
        })}
      </div>
      
      {errors.goals && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="error-message"
        >
          {errors.goals}
        </motion.div>
      )}
    </motion.div>
  )

  const renderSignupStep = () => (
    <motion.div
      initial={{ opacity: 0, x: 30 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -30 }}
      transition={{ duration: 0.5 }}
      className="step-content signup-step"
    >
      {errors.general && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="error-banner"
        >
          {errors.general}
        </motion.div>
      )}
      
      <div className="form-container">
        <div className="form-group">
          <label htmlFor="name">Full Name</label>
          <input
            id="name"
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter your full name"
            className={`form-input ${errors.name ? 'error' : ''}`}
            disabled={isLoading}
          />
          {errors.name && (
            <div className="field-error">{errors.name}</div>
          )}
        </div>
        
        <div className="form-group">
          <label htmlFor="email">Email Address</label>
          <div className="input-wrapper">
            <Mail className="input-icon" size={16} />
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="Enter your email address"
              className={`form-input with-icon ${errors.email ? 'error' : ''}`}
              disabled={isLoading}
            />
          </div>
          {errors.email && (
            <div className="field-error">{errors.email}</div>
          )}
        </div>
        
        <div className="form-group">
          <label htmlFor="password">Password</label>
          <div className="input-wrapper">
            <Lock className="input-icon" size={16} />
            <input
              id="password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="Create a strong password"
              className={`form-input with-icon ${errors.password ? 'error' : ''}`}
              disabled={isLoading}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.password && (
            <div className="field-error">{errors.password}</div>
          )}
        </div>
        
        <div className="form-group">
          <label htmlFor="confirmPassword">Confirm Password</label>
          <div className="input-wrapper">
            <Lock className="input-icon" size={16} />
            <input
              id="confirmPassword"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              placeholder="Confirm your password"
              className={`form-input with-icon ${errors.confirmPassword ? 'error' : ''}`}
              disabled={isLoading}
            />
            <button
              type="button"
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
            </button>
          </div>
          {errors.confirmPassword && (
            <div className="field-error">{errors.confirmPassword}</div>
          )}
        </div>
      </div>
    </motion.div>
  )

  const renderStepContent = () => {
    switch (steps[currentStep].id) {
      case 'welcome':
        return renderWelcomeStep()
      case 'goals':
        return renderGoalsStep()
      case 'signup':
        return renderSignupStep()
      default:
        return null
    }
  }

  return (
    <div className="onboarding-container">
      <div className="onboarding-background">
        <div className="bg-pattern" />
        <div className="bg-gradient" />
      </div>
      
      <div className="onboarding-content">
        <div className="onboarding-card">
          <div className="onboarding-header">
            <div className="logo">
              <Sparkles size={24} />
              <span>TweetCrafter</span>
            </div>
            
            <div className="progress-container">
              <div className="progress-bar">
                <motion.div
                  className="progress-fill"
                  initial={{ width: 0 }}
                  animate={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
              <span className="progress-text">
                Step {currentStep + 1} of {steps.length}
              </span>
            </div>
          </div>

          <div className="step-container">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
              className="step-header"
            >
              <h1 className="step-title">{steps[currentStep].title}</h1>
              <p className="step-subtitle">{steps[currentStep].subtitle}</p>
            </motion.div>
            
            <AnimatePresence mode="wait">
              {renderStepContent()}
            </AnimatePresence>
          </div>

          <div className="onboarding-navigation">
            <div className="nav-left">
              {currentStep > 0 && (
                <button
                  className="nav-button secondary"
                  onClick={handleBack}
                  disabled={isLoading}
                >
                  Back
                </button>
              )}
            </div>
            
            <div className="nav-right">
              {steps[currentStep].id !== 'welcome' && (
                <button
                  className="nav-button primary"
                  onClick={handleNext}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="button-loading">
                      <div className="spinner" />
                      {steps[currentStep].id === 'signup' ? 'Creating...' : 'Loading...'}
                    </div>
                  ) : (
                    <>
                      {steps[currentStep].id === 'goals' ? 'Continue' :
                       steps[currentStep].id === 'signup' ? 'Create Account' : 'Next'}
                      <ArrowRight size={14} />
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        .onboarding-container {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: flex-start;
          justify-content: center;
          z-index: 1000;
          overflow-y: auto;
          padding: ${SPACING.sm};
          min-height: 100vh;
          box-sizing: border-box;
        }

        .onboarding-background {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
        }

        .bg-pattern {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image:
            radial-gradient(circle at 25% 25%, ${COLORS.primary}15 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, ${COLORS.secondary}15 0%, transparent 50%);
        }

        .bg-gradient {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg,
            ${COLORS.background}f0 0%,
            ${COLORS.gray50}f0 100%);
          backdrop-filter: blur(20px);
        }

        .onboarding-content {
          position: relative;
          z-index: 1;
          width: 100%;
          max-width: 800px;
          margin: 0 auto;
        }

        .onboarding-card {
          background: ${COLORS.background};
          border-radius: 16px;
          border: 1px solid ${COLORS.gray200};
          overflow: hidden;
          width: 100%;
          display: flex;
          flex-direction: column;
          margin: auto 0;
        }

        .onboarding-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: ${SPACING.md} ${SPACING.lg};
          border-bottom: 1px solid ${COLORS.gray200};
        }

        .logo {
          display: flex;
          align-items: center;
          gap: ${SPACING.sm};
          color: ${COLORS.primary};
          font-size: 1.25rem;
          font-weight: 700;
        }

        .progress-container {
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          gap: ${SPACING.xs};
        }

        .progress-bar {
          width: 150px;
          height: 3px;
          background: ${COLORS.gray200};
          border-radius: 2px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, ${COLORS.primary}, ${COLORS.secondary});
          border-radius: 2px;
        }

        .progress-text {
          font-size: 0.75rem;
          color: ${COLORS.textSecondary};
          font-weight: 500;
        }

        .step-container {
          flex: 1;
          padding: ${SPACING.md} ${SPACING.lg};
          display: flex;
          flex-direction: column;
          min-height: 0;
        }

        .step-header {
          text-align: center;
          margin-bottom: ${SPACING.md};
        }

        .step-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.xs} 0;
          line-height: 1.3;
        }

        .step-subtitle {
          color: ${COLORS.textSecondary};
          margin: 0;
          font-size: 0.875rem;
          font-weight: 500;
        }

        .step-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: flex-start;
          min-height: 0;
        }

        /* Welcome Step */
        .welcome-step {
          text-align: center;
          overflow-y: auto;
          flex: 1;
        }

        .hero-section {
          margin-bottom: ${SPACING.lg};
        }

        .hero-icon {
          background: linear-gradient(135deg, ${COLORS.primary}, ${COLORS.secondary});
          color: white;
          width: 48px;
          height: 48px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto ${SPACING.sm} auto;
        }

        .hero-stats {
          display: flex;
          justify-content: center;
          gap: ${SPACING.md};
          margin-bottom: ${SPACING.md};
        }

        .stat {
          display: flex;
          align-items: center;
          gap: ${SPACING.xs};
          color: ${COLORS.textSecondary};
          font-weight: 500;
          font-size: 0.75rem;
        }

        .stat-icon {
          color: ${COLORS.warning};
          width: 14px;
          height: 14px;
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: ${SPACING.sm};
          margin-bottom: ${SPACING.lg};
        }

        .feature-card {
          background: ${COLORS.gray50};
          padding: ${SPACING.sm};
          border-radius: 8px;
          border: 1px solid ${COLORS.gray200};
          transition: all 0.3s ease;
          text-align: center;
        }

        .feature-card:hover {
          transform: translateY(-1px);
        }

        .feature-icon {
          color: ${COLORS.primary};
          margin-bottom: ${SPACING.xs};
          width: 16px;
          height: 16px;
        }

        .feature-card h4 {
          font-size: 0.75rem;
          font-weight: 600;
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.xs} 0;
        }

        .feature-card p {
          color: ${COLORS.textSecondary};
          margin: 0;
          line-height: 1.3;
          font-size: 0.625rem;
        }

        .cta-section {
          text-align: center;
        }

        .cta-note {
          color: ${COLORS.textMuted};
          margin: ${SPACING.xs} 0 0 0;
          font-size: 0.75rem;
        }

        /* Goals Step */
        .goals-step {
          text-align: center;
          overflow-y: auto;
          flex: 1;
        }

        .goals-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
          gap: ${SPACING.sm};
          margin-bottom: ${SPACING.md};
        }

        .goal-card {
          background: ${COLORS.background};
          border: 2px solid ${COLORS.gray200};
          border-radius: 8px;
          padding: ${SPACING.sm};
          cursor: pointer;
          transition: all 0.3s ease;
          position: relative;
          text-align: center;
        }

        .goal-card:hover {
          border-color: ${COLORS.primary}60;
          transform: translateY(-1px);
        }

        .goal-card.selected {
          border-color: ${COLORS.primary};
          background: ${COLORS.primary}05;
          transform: translateY(-1px);
        }

        .goal-icon {
          margin-bottom: ${SPACING.xs};
        }

        .goal-card h4 {
          font-size: 0.75rem;
          font-weight: 600;
          color: ${COLORS.textPrimary};
          margin: 0 0 ${SPACING.xs} 0;
        }

        .goal-card p {
          color: ${COLORS.textSecondary};
          margin: 0;
          line-height: 1.3;
          font-size: 0.625rem;
        }

        .goal-check {
          position: absolute;
          top: ${SPACING.xs};
          right: ${SPACING.xs};
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: ${COLORS.primary};
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        .goal-card.selected .goal-check {
          opacity: 1;
        }

        /* Signup Step */
        .signup-step {
          max-width: 400px;
          margin: 0 auto;
          overflow-y: auto;
          flex: 1;
        }

        .form-container {
          text-align: left;
          padding-bottom: ${SPACING.sm};
        }

        .error-banner {
          background: ${COLORS.error}10;
          border: 1px solid ${COLORS.error}30;
          border-radius: 8px;
          padding: ${SPACING.sm};
          margin-bottom: ${SPACING.sm};
          text-align: center;
          color: ${COLORS.error};
          font-weight: 500;
          font-size: 0.75rem;
        }

        .form-group {
          margin-bottom: ${SPACING.xs};
          text-align: left;
        }

        .form-group label {
          display: block;
          font-weight: 600;
          color: ${COLORS.textPrimary};
          margin-bottom: ${SPACING.xs};
          font-size: 0.75rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .input-wrapper {
          position: relative;
          margin-bottom: ${SPACING.xs};
        }

        .input-icon {
          position: absolute;
          left: ${SPACING.sm};
          top: 50%;
          transform: translateY(-50%);
          color: ${COLORS.textMuted};
          z-index: 1;
        }

        .password-toggle {
          position: absolute;
          right: ${SPACING.sm};
          top: 50%;
          transform: translateY(-50%);
          background: none;
          border: none;
          color: ${COLORS.textMuted};
          cursor: pointer;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .password-toggle:hover {
          color: ${COLORS.textPrimary};
        }

        .form-input {
          width: 100%;
          padding: ${SPACING.xs} ${SPACING.sm};
          border: 2px solid ${COLORS.gray200};
          border-radius: 6px;
          font-size: 0.75rem;
          transition: all 0.2s ease;
          background: ${COLORS.background};
        }

        .form-input.with-icon {
          padding-left: 2.5rem;
        }

        .form-input:focus {
          outline: none;
          border-color: ${COLORS.primary};
          transform: translateY(-1px);
        }

        .form-input.error {
          border-color: ${COLORS.error};
          background: ${COLORS.error}05;
        }

        .form-input.error:focus {
          border-color: ${COLORS.error};
        }

        .form-input:disabled {
          background-color: ${COLORS.gray100};
          cursor: not-allowed;
          opacity: 0.6;
        }

        .field-error {
          color: ${COLORS.error};
          font-size: 0.625rem;
          margin-top: ${SPACING.xs};
          font-weight: 500;
        }

        .error-message {
          color: ${COLORS.error};
          font-size: 0.75rem;
          margin-top: ${SPACING.sm};
        }

        /* Navigation */
        .onboarding-navigation {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: ${SPACING.sm} ${SPACING.lg};
          border-top: 1px solid ${COLORS.gray200};
          background: ${COLORS.gray50};
        }

        .nav-left, .nav-right {
          display: flex;
          gap: ${SPACING.sm};
          align-items: center;
        }

        .nav-button {
          border: none;
          border-radius: 6px;
          font-size: 0.75rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: ${SPACING.xs};
          min-height: 32px;
          padding: 0 ${SPACING.md};
        }

        .nav-button.primary {
          background: ${COLORS.primary};
          color: white;
        }

        .nav-button.primary:hover:not(:disabled) {
          background: ${COLORS.primary}dd;
          transform: translateY(-1px);
        }

        .nav-button.secondary {
          background: ${COLORS.background};
          color: ${COLORS.textSecondary};
          border: 2px solid ${COLORS.gray300};
        }

        .nav-button.secondary:hover:not(:disabled) {
          border-color: ${COLORS.gray400};
          color: ${COLORS.textPrimary};
        }

        .nav-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none !important;
        }

        .primary-button {
          background: ${COLORS.primary};
          color: white;
          border: none;
          padding: ${SPACING.sm} ${SPACING.md};
          border-radius: 6px;
          font-size: 0.75rem;
          font-weight: 600;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: ${SPACING.xs};
          min-height: 32px;
        }

        .primary-button.large {
          padding: ${SPACING.sm} ${SPACING.lg};
          font-size: 0.875rem;
          min-height: 36px;
        }

        .primary-button:hover:not(:disabled) {
          background: ${COLORS.primary}dd;
          transform: translateY(-1px);
        }

        .primary-button:disabled {
          background: ${COLORS.gray400};
          cursor: not-allowed;
          transform: none;
        }

        .button-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: ${SPACING.xs};
        }

        .spinner {
          width: 12px;
          height: 12px;
          border: 2px solid transparent;
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
          .onboarding-container {
            padding: ${SPACING.xs};
          }

          .onboarding-header {
            flex-direction: column;
            gap: ${SPACING.xs};
            text-align: center;
            padding: ${SPACING.sm};
          }

          .progress-container {
            align-items: center;
          }

          .step-container {
            padding: ${SPACING.sm};
          }

          .step-title {
            font-size: 1.25rem;
          }

          .step-subtitle {
            font-size: 0.75rem;
          }

          .features-grid {
            grid-template-columns: 1fr;
            gap: ${SPACING.xs};
          }

          .goals-grid {
            grid-template-columns: 1fr;
            gap: ${SPACING.xs};
          }

          .hero-stats {
            flex-direction: row;
            gap: ${SPACING.sm};
          }

          .form-group {
            margin-bottom: ${SPACING.xs};
          }

          .signup-step {
            max-width: 100%;
          }

          .onboarding-navigation {
            padding: ${SPACING.xs};
            flex-direction: row;
            gap: ${SPACING.xs};
          }

          .nav-left, .nav-right {
            flex: 1;
            justify-content: center;
          }

          .nav-button {
            min-width: 60px;
            font-size: 0.625rem;
            min-height: 28px;
          }
        }

        @media (max-height: 700px) {
          .onboarding-container {
            padding: ${SPACING.xs};
          }

          .step-header {
            margin-bottom: ${SPACING.xs};
          }

          .step-title {
            font-size: 1.125rem;
          }

          .step-subtitle {
            font-size: 0.75rem;
          }

          .form-group {
            margin-bottom: ${SPACING.xs};
          }

          .onboarding-header {
            padding: ${SPACING.xs};
          }

          .step-container {
            padding: ${SPACING.xs};
          }

          .onboarding-navigation {
            padding: ${SPACING.xs};
          }

          .hero-section {
            margin-bottom: ${SPACING.sm};
          }

          .hero-icon {
            width: 32px;
            height: 32px;
            margin-bottom: ${SPACING.xs};
          }

          .features-grid {
            gap: ${SPACING.xs};
          }

          .goals-grid {
            gap: ${SPACING.xs};
          }
        }
      `}</style>
    </div>
  )
}

export default OnboardingFlow
