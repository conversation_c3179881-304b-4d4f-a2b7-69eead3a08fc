// Mock API functions for TweetCrafter application
import {
  mockUser,
  niches,
  mockGeneratedPosts,
  mockScheduledPosts,
  mockPublishedPosts,
  mockTemplates,
  mockAnalytics
} from '../data/mockData.js';

// Simulate API delay
const delay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Authentication API
export const authApi = {
  async signUp(email, password) {
    await delay(1500);
    return {
      success: true,
      user: { ...mockUser, email },
      token: 'mock_jwt_token'
    };
  },

  async signIn(email, password) {
    await delay(1000);
    return {
      success: true,
      user: mockUser,
      token: 'mock_jwt_token'
    };
  },

  async connectTwitter() {
    await delay(2000);
    return {
      success: true,
      user: { ...mockUser, isConnected: true }
    };
  }
};

// User API
export const userApi = {
  async getProfile() {
    await delay(500);
    return mockUser;
  },

  async updateProfile(updates) {
    await delay(800);
    return { ...mockUser, ...updates };
  }
};

// AI Generation API
export const aiApi = {
  async generatePosts(niche, prompt) {
    await delay(3000); // Simulate AI processing time
    
    // Generate variations based on the prompt
    const variations = [
      {
        id: `generated_${Date.now()}_1`,
        text: `🚀 ${prompt} - Here's what I discovered: [AI generated content based on your prompt] #${niche.replace(/\s+/g, '')} #AI`,
        media: {
          type: 'image',
          url: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=300&fit=crop',
          alt: 'AI generated content'
        },
        status: 'pending',
        createdAt: new Date().toISOString(),
        niche,
        engagement: { likes: 0, retweets: 0, replies: 0 }
      },
      {
        id: `generated_${Date.now()}_2`,
        text: `💡 Thoughts on: ${prompt}\n\nKey insights:\n• Point 1\n• Point 2\n• Point 3\n\nWhat's your take? 🤔`,
        media: null,
        status: 'pending',
        createdAt: new Date().toISOString(),
        niche,
        engagement: { likes: 0, retweets: 0, replies: 0 }
      },
      {
        id: `generated_${Date.now()}_3`,
        text: `🎯 ${prompt} - Thread 🧵\n\n1/ Let me break this down for you...\n\n[This would be the start of a Twitter thread]`,
        media: null,
        status: 'pending',
        createdAt: new Date().toISOString(),
        niche,
        engagement: { likes: 0, retweets: 0, replies: 0 }
      }
    ];

    return variations;
  },

  async getNiches() {
    await delay(300);
    return niches;
  }
};

// Posts API
export const postsApi = {
  async getGeneratedPosts() {
    await delay(500);
    return mockGeneratedPosts;
  },

  async getScheduledPosts() {
    await delay(500);
    return mockScheduledPosts;
  },

  async getPublishedPosts() {
    await delay(500);
    return mockPublishedPosts;
  },

  async approvePost(postId) {
    await delay(800);
    return { success: true, postId, status: 'approved' };
  },

  async deletePost(postId) {
    await delay(500);
    return { success: true, postId };
  },

  async editPost(postId, updates) {
    await delay(800);
    return { success: true, postId, updates };
  },

  async schedulePost(postId, scheduledTime) {
    await delay(1000);
    return {
      success: true,
      postId,
      scheduledFor: scheduledTime,
      status: 'scheduled'
    };
  },

  async publishNow(postId) {
    await delay(1500);
    return {
      success: true,
      postId,
      publishedAt: new Date().toISOString(),
      status: 'published'
    };
  }
};

// Templates API
export const templatesApi = {
  async getTemplates() {
    await delay(500);
    return mockTemplates;
  },

  async createTemplate(template) {
    await delay(1000);
    const newTemplate = {
      id: `template_${Date.now()}`,
      ...template,
      isActive: false,
      lastRun: null,
      createdAt: new Date().toISOString()
    };
    return newTemplate;
  },

  async updateTemplate(templateId, updates) {
    await delay(800);
    return { success: true, templateId, updates };
  },

  async deleteTemplate(templateId) {
    await delay(500);
    return { success: true, templateId };
  },

  async testTemplate(templateId, testData) {
    await delay(1500);
    return {
      success: true,
      result: {
        text: "Test output: This is what your template would generate with the provided test data.",
        media: null
      },
      logs: ["Template executed successfully", "Generated content", "Ready for deployment"]
    };
  },

  async deployTemplate(templateId) {
    await delay(1000);
    return {
      success: true,
      templateId,
      status: 'active',
      deployedAt: new Date().toISOString()
    };
  }
};

// Analytics API
export const analyticsApi = {
  async getOverview() {
    await delay(800);
    return mockAnalytics.overview;
  },

  async getWeeklyStats() {
    await delay(600);
    return mockAnalytics.weeklyStats;
  },

  async getNichePerformance() {
    await delay(700);
    return mockAnalytics.nichePerformance;
  },

  async getPostAnalytics(postId) {
    await delay(500);
    const post = mockPublishedPosts.find(p => p.id === postId);
    return post ? post.engagement : null;
  }
};

// Scheduling API
export const schedulingApi = {
  async getAvailableSlots(date) {
    await delay(400);
    // Return available time slots for the given date
    return [
      '09:00', '10:00', '11:00', '12:00', '13:00', 
      '14:00', '15:00', '16:00', '17:00', '18:00'
    ];
  },

  async schedulePost(postId, dateTime) {
    await delay(1000);
    return {
      success: true,
      postId,
      scheduledFor: dateTime,
      message: 'Post scheduled successfully!'
    };
  },

  async reschedulePost(postId, newDateTime) {
    await delay(800);
    return {
      success: true,
      postId,
      scheduledFor: newDateTime,
      message: 'Post rescheduled successfully!'
    };
  },

  async cancelScheduledPost(postId) {
    await delay(500);
    return {
      success: true,
      postId,
      message: 'Scheduled post cancelled successfully!'
    };
  }
};
