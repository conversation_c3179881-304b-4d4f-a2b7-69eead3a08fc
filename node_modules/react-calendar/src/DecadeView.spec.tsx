import { describe, expect, it } from 'vitest';

import { render } from '@testing-library/react';

import DecadeView from './DecadeView.js';

describe('DecadeView', () => {
  const defaultProps = {
    activeStartDate: new Date(2017, 0, 1),
    valueType: 'year',
  } satisfies React.ComponentProps<typeof DecadeView>;

  it('renders proper view when given activeStartDate', () => {
    const activeStartDate = new Date(2011, 0, 1);

    const { container } = render(
      <DecadeView {...defaultProps} activeStartDate={activeStartDate} />,
    );

    const firstDayTile = container.querySelector('.react-calendar__tile');

    expect(firstDayTile).toHaveTextContent(`${activeStartDate.getFullYear()}`);
  });

  it('applies tileClassName to its tiles when given a string', () => {
    const tileClassName = 'testClassName';

    const { container } = render(<DecadeView {...defaultProps} tileClassName={tileClassName} />);

    const firstDayTile = container.querySelector('.react-calendar__tile');

    expect(firstDayTile).toHaveClass(tileClassName);
  });

  it('applies tileClassName to its tiles conditionally when given a function that returns a string', () => {
    const activeStartDate = new Date(2011, 0, 1);
    const tileClassNameFn = ({ date }: { date: Date }) => {
      if (date.getTime() === activeStartDate.getTime()) {
        return 'firstDayOfTheMonth';
      }

      return null;
    };

    const { container } = render(
      <DecadeView
        {...defaultProps}
        activeStartDate={activeStartDate}
        tileClassName={tileClassNameFn}
      />,
    );

    const tiles = container.querySelectorAll('.react-calendar__tile');

    const firstDayTile = tiles[0];
    const secondDayTile = tiles[1];

    expect(firstDayTile).toHaveClass('firstDayOfTheMonth');
    expect(secondDayTile).not.toHaveClass('firstDayOfTheMonth');
  });

  it('renders tileContent in its tiles when given a node', () => {
    const tileContent = <div className="testContent" />;

    const { container } = render(<DecadeView {...defaultProps} tileContent={tileContent} />);

    const firstDayTile = container.querySelector('.react-calendar__tile') as HTMLDivElement;
    const firstDayTileContent = firstDayTile.querySelector('.testContent');

    expect(firstDayTileContent).toBeInTheDocument();
  });

  it('renders tileContent in its tiles conditionally when given a function that returns a node', () => {
    const activeStartDate = new Date(2011, 0, 1);
    const tileContentFn = ({ date }: { date: Date }) => {
      if (date.getTime() === activeStartDate.getTime()) {
        return <div className="testContent" />;
      }

      return null;
    };

    const { container } = render(
      <DecadeView
        {...defaultProps}
        activeStartDate={activeStartDate}
        tileContent={tileContentFn}
      />,
    );

    const tiles = container.querySelectorAll('.react-calendar__tile');

    const firstDayTile = tiles[0] as HTMLDivElement;
    const secondDayTile = tiles[1] as HTMLDivElement;

    const firstDayTileContent = firstDayTile.querySelector('.testContent');
    const secondDayTileContent = secondDayTile.querySelector('.testContent');

    expect(firstDayTileContent).toBeInTheDocument();
    expect(secondDayTileContent).not.toBeInTheDocument();
  });

  it('displays decade view with custom year formatting', () => {
    const formatYear = () => 'Year';

    const { container } = render(<DecadeView {...defaultProps} formatYear={formatYear} />);

    const year = container.querySelector('.react-calendar__decade-view__years__year');

    expect(year).toHaveTextContent('Year');
  });
});
