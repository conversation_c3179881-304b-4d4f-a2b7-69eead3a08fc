import { describe, expect, it, vi } from 'vitest';
import { fireEvent, render } from '@testing-library/react';

import Month from './Month.js';

const tileProps = {
  activeStartDate: new Date(2018, 0, 1),
  classes: ['react-calendar__tile'],
  date: new Date(2018, 0, 1),
};

describe('Month', () => {
  it('applies given classNames properly', () => {
    const { container } = render(
      <Month
        {...tileProps}
        classes={['react-calendar__tile', 'react-calendar__tile--flag']}
        tileClassName={() => 'testFunctionClassName'}
      />,
    );

    const wrapper = container.querySelector('.react-calendar__tile');

    expect(wrapper).toHaveClass('react-calendar__tile');
    expect(wrapper).toHaveClass('react-calendar__tile--flag');
    expect(wrapper).toHaveClass('react-calendar__year-view__months__month');
    expect(wrapper).toHaveClass('testFunctionClassName');
  });

  it('renders component with proper abbreviation', () => {
    const { container } = render(<Month {...tileProps} date={new Date(2018, 0, 1)} />);

    const abbr = container.querySelector('abbr');

    expect(abbr).toBeInTheDocument();
    expect(abbr).toHaveAccessibleName('January 2018');
    expect(container).toHaveTextContent('January');
  });

  it("is disabled when date is before beginning of minDate's month", () => {
    const { container } = render(
      <Month {...tileProps} date={new Date(2018, 6, 1)} minDate={new Date(2018, 7, 1)} />,
    );

    const tile = container.querySelector('.react-calendar__tile');

    expect(tile).toBeDisabled();
  });

  it("is not disabled when date is after beginning of minDate's month", () => {
    const { container } = render(
      <Month {...tileProps} date={new Date(2018, 0, 1)} minDate={new Date(2018, 0, 1)} />,
    );

    const tile = container.querySelector('.react-calendar__tile');

    expect(tile).toBeEnabled();
  });

  it("is disabled when date is after end of maxDate's month", () => {
    const { container } = render(
      <Month {...tileProps} date={new Date(2018, 6, 1)} maxDate={new Date(2018, 5, 1)} />,
    );

    const tile = container.querySelector('.react-calendar__tile');

    expect(tile).toBeDisabled();
  });

  it("is not disabled when date is before end of maxDate's month", () => {
    const { container } = render(
      <Month {...tileProps} date={new Date(2018, 0, 1)} maxDate={new Date(2018, 0, 1)} />,
    );

    const tile = container.querySelector('.react-calendar__tile');

    expect(tile).toBeEnabled();
  });

  it('calls onClick callback when clicked and sends proper date as an argument', () => {
    const date = new Date(2018, 0, 1);
    const onClick = vi.fn();

    const { container } = render(<Month {...tileProps} date={date} onClick={onClick} />);

    fireEvent.click(container.querySelector('.react-calendar__tile') as HTMLDivElement);

    expect(onClick).toHaveBeenCalled();
    expect(onClick).toHaveBeenCalledWith(date, expect.any(Object));
  });

  it('calls onMouseOver callback when hovered and sends proper date as an argument', () => {
    const date = new Date(2018, 0, 1);
    const onMouseOver = vi.fn();

    const { container } = render(<Month {...tileProps} date={date} onMouseOver={onMouseOver} />);

    const tile = container.querySelector('.react-calendar__tile') as HTMLDivElement;
    fireEvent.mouseOver(tile);

    expect(onMouseOver).toHaveBeenCalled();
    expect(onMouseOver).toHaveBeenCalledWith(date);
  });

  it('calls onMouseOver callback when focused and sends proper date as an argument', () => {
    const date = new Date(2018, 0, 1);
    const onMouseOver = vi.fn();

    const { container } = render(<Month {...tileProps} date={date} onMouseOver={onMouseOver} />);

    const tile = container.querySelector('.react-calendar__tile') as HTMLDivElement;
    fireEvent.focus(tile);

    expect(onMouseOver).toHaveBeenCalled();
    expect(onMouseOver).toHaveBeenCalledWith(date);
  });

  it('renders tileContent properly', () => {
    const { container } = render(
      <Month {...tileProps} tileContent={<div className="testContent" />} />,
    );

    const testContent = container.querySelector('.testContent');

    expect(testContent).toBeInTheDocument();
  });

  it('renders tileContent function result properly and sends proper arguments to it', () => {
    const date = new Date(2018, 0, 1);
    const tileContent = vi.fn();
    tileContent.mockReturnValue(<div className="testContent" />);

    const { container } = render(<Month {...tileProps} date={date} tileContent={tileContent} />);

    const testContent = container.querySelector('.testContent');

    expect(tileContent).toHaveBeenCalled();
    expect(tileContent).toHaveBeenCalledWith({
      activeStartDate: tileProps.activeStartDate,
      date,
      view: 'year',
    });
    expect(testContent).toBeInTheDocument();
  });

  it('uses formatMonth if given', () => {
    const locale = 'en-US';
    const date = new Date(2018, 0, 1);
    const formatMonth = vi.fn();
    formatMonth.mockReturnValue('Mock format');

    const { container } = render(
      <Month {...tileProps} date={date} formatMonth={formatMonth} locale={locale} />,
    );

    const tile = container.querySelector('.react-calendar__tile');

    expect(formatMonth).toHaveBeenCalled();
    expect(formatMonth).toHaveBeenCalledWith(locale, date);
    expect(tile).toHaveTextContent('Mock format');
  });

  it('uses formatMonthYear if given', () => {
    const locale = 'en-US';
    const date = new Date(2018, 0, 1);
    const formatMonthYear = vi.fn();
    formatMonthYear.mockReturnValue('Mock format');

    const { container } = render(
      <Month {...tileProps} date={date} formatMonthYear={formatMonthYear} locale={locale} />,
    );

    const abbr = container.querySelector('abbr');

    expect(formatMonthYear).toHaveBeenCalled();
    expect(formatMonthYear).toHaveBeenCalledWith(locale, date);
    expect(abbr).toHaveAccessibleName('Mock format');
  });
});
