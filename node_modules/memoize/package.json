{"name": "memoize", "version": "10.1.0", "description": "Memoize functions - An optimization used to speed up consecutive function calls by caching the result of calls with identical input", "license": "MIT", "repository": "sindresorhus/memoize", "funding": "https://github.com/sindresorhus/memoize?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./distribution/index.d.ts", "default": "./distribution/index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava && npm run build && tsd --typings distribution/index.d.ts", "build": "del-cli distribution && tsc", "prepack": "npm run build"}, "files": ["distribution"], "keywords": ["memoize", "function", "mem", "memoization", "cache", "caching", "optimize", "performance", "ttl", "expire", "promise"], "dependencies": {"mimic-function": "^5.0.1"}, "devDependencies": {"@sindresorhus/tsconfig": "^6.0.0", "@types/serialize-javascript": "^5.0.4", "ava": "^6.1.3", "del-cli": "^5.1.0", "delay": "^6.0.0", "serialize-javascript": "^6.0.2", "ts-node": "^10.9.2", "tsd": "^0.31.1", "xo": "^0.59.3"}, "ava": {"timeout": "1m", "extensions": {"ts": "module"}, "nodeArguments": ["--loader=ts-node/esm"], "workerThreads": false}, "xo": {"rules": {"@typescript-eslint/no-unsafe-return": "off"}}}