{"compilerOptions": {"module": "esnext", "lib": ["esnext", "dom", "dom.iterable"], "target": "es2020", "declaration": true, "declarationMap": true, "outDir": "./dist", "strict": true, "skipLibCheck": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "moduleResolution": "node", "jsx": "react", "esModuleInterop": true}, "include": ["src", "tsup.config.ts", "setupTests.ts", "vitest.config.ts"]}