# TweetCrafter 🚀

**AI-powered Twitter content automation platform built with React**

TweetCrafter is a comprehensive web application that helps content creators automate their Twitter presence using AI-generated content, advanced templating, and intelligent scheduling.

## ✨ Features

### 🤖 AI Content Generation
- **Niche-based content creation** - Select from various content niches
- **Custom prompt input** - Provide specific instructions for AI generation
- **Multi-post generation** - Generate multiple post variations at once
- **Real-time preview** - See how posts will look before publishing
- **Content approval workflow** - Review and approve AI-generated content

### 📝 Advanced Templating System
- **Code-based templates** - Write JavaScript functions for dynamic content
- **Live testing console** - Test templates with real data
- **Template management** - Create, edit, and deploy templates
- **Version control** - Track template changes and deployments
- **Syntax highlighting** - Built-in code editor with JavaScript support

### 📅 Intelligent Scheduling
- **Visual calendar interface** - Drag-and-drop scheduling
- **Optimal time suggestions** - AI-recommended posting times
- **Bulk scheduling** - Schedule multiple posts at once
- **Time zone support** - Automatic time zone handling
- **Recurring posts** - Set up repeating content schedules

### 📊 Analytics Dashboard
- **Performance metrics** - Track engagement, reach, and growth
- **Visual charts** - Beautiful data visualizations
- **Trend analysis** - Identify top-performing content
- **Export capabilities** - Download analytics reports
- **Real-time updates** - Live performance tracking

### 🎨 Modern UI/UX
- **Responsive design** - Works on desktop, tablet, and mobile
- **Dark/light themes** - Customizable appearance
- **Smooth animations** - Framer Motion powered transitions
- **Accessibility** - WCAG compliant interface
- **Notification system** - Real-time feedback and alerts

## 🛠️ Technology Stack

### Frontend
- **React 18** - Modern React with hooks and concurrent features
- **Vite** - Lightning-fast build tool and dev server
- **React Router** - Client-side routing
- **Framer Motion** - Smooth animations and transitions
- **Lucide React** - Beautiful icon library
- **Date-fns** - Modern date utility library

### Styling
- **CSS-in-JS** - Styled JSX for component-scoped styles
- **Design System** - Consistent colors, typography, and spacing
- **Responsive Design** - Mobile-first approach
- **Custom Components** - Reusable UI component library

### State Management
- **React Hooks** - useState, useEffect, useContext
- **Local Storage** - Persistent user preferences
- **Mock API** - Simulated backend for development

## 🚀 Getting Started

### Prerequisites
- Node.js 16+ 
- npm or yarn package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tweetcrafter
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## 📱 Application Structure

```
src/
├── components/          # Reusable UI components
│   ├── AI/             # AI automation components
│   ├── Dashboard/      # Dashboard widgets
│   ├── Layout/         # Layout components (Header, Sidebar)
│   ├── Onboarding/     # User onboarding flow
│   ├── Scheduling/     # Calendar and scheduling components
│   ├── Templating/     # Template editor and management
│   └── UI/             # Base UI components (Button, Card, etc.)
├── pages/              # Main application pages
├── data/               # Mock data and API simulation
├── utils/              # Utility functions and constants
└── api/                # API layer and mock implementations
```

## 🎯 Key Features Walkthrough

### 1. Onboarding Experience
- **Welcome screen** with app introduction
- **Account creation** with form validation
- **Twitter integration** setup (simulated)
- **Smooth transitions** between steps

### 2. Dashboard Overview
- **Analytics cards** showing key metrics
- **Scheduled posts preview** for upcoming content
- **Recent performance** tracking
- **Quick action buttons** for common tasks

### 3. AI Content Generation
- **Niche selection** from predefined categories
- **Prompt input** with character counting
- **Loading animations** during generation
- **Post review interface** with edit capabilities
- **Bulk actions** for approval and scheduling

### 4. Template Management
- **Code editor** with syntax highlighting
- **Live testing** with custom data input
- **Template library** with search and filtering
- **Deployment system** for activating templates
- **Version tracking** and rollback capabilities

### 5. Scheduling Interface
- **Calendar view** with visual post indicators
- **Time slot picker** with optimal suggestions
- **List view** with filtering options
- **Bulk operations** for managing multiple posts
- **Status tracking** (scheduled, published, paused)

## 🎨 Design System

### Color Palette
- **Primary**: #3B82F6 (Bright Blue)
- **Secondary**: #10B981 (Teal)
- **Accent**: #8B5CF6 (Purple)
- **Success**: #10B981
- **Warning**: #F59E0B
- **Error**: #EF4444

### Typography
- **Font Family**: Inter, -apple-system, BlinkMacSystemFont
- **Font Weights**: 400 (normal), 500 (medium), 600 (semibold), 700 (bold)
- **Responsive scaling** across different screen sizes

### Spacing System
- **Base unit**: 4px
- **Scale**: 4px, 8px, 16px, 24px, 32px, 48px, 64px, 96px

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Code Style
- **ESLint** configuration for code quality
- **Prettier** for consistent formatting
- **Component-based architecture**
- **Custom hooks** for reusable logic

### Mock Data
The application uses comprehensive mock data to simulate:
- User profiles and authentication
- Post generation and management
- Analytics and performance metrics
- Template library and testing
- Scheduling and calendar data

## 🌟 Future Enhancements

### Planned Features
- **Real Twitter API integration**
- **Advanced analytics with ML insights**
- **Team collaboration features**
- **Content calendar templates**
- **A/B testing for posts**
- **Multi-platform support** (LinkedIn, Facebook)
- **Advanced AI models** for content generation
- **Custom branding options**

### Technical Improvements
- **Backend API development**
- **Database integration**
- **User authentication system**
- **Real-time notifications**
- **Progressive Web App (PWA)**
- **Offline functionality**

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

For support, email <EMAIL> or join our Discord community.

---

**Built with ❤️ by the TweetCrafter team**
